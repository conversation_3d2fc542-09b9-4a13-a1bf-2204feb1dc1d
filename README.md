# VitaminStation API

## Installation

1. Clone the repository
2. Add `.env` file in the root directory with the following content:

```env
HTTP_PORT=3000
NODE_ENV=development
POSTGRES_HOST=db
POSTGRES_PORT=5432
POSTGRES_PORT_EXT=5495
POSTGRES_DB=
POSTGRES_USER=
POSTGRES_PASSWORD=
JWT_ACCESS_TOKEN_SECRET=
JWT_ACCESS_TOKEN_EXPIRATION_S=3600
JWT_REFRESH_TOKEN_SECRET=
JWT_REFRESH_TOKEN_EXPIRATION_S=604800
GROUP_INVITATION_SECRET=
GROUP_INVITATION_BASE_URL=https://invite-to-group.pl
ADMIN_SECRET_KEY=
GOOGLE_AUTH_CLIENT_ID=
GOOGLE_AUTH_CLIENT_SECRET=
GOOGLE_AUTH_REDIRECT_URI=
SENDGRID_HOST=
SENDGRID_API_KEY=
SENDGRID_API_USERNAME=
SENDGRID_MAIL_FROM=
```

3. Install dependencies

```bash
$ docker compose up
```

## Migrations

To create a new migration, run the following command:

```bash
# enter the container
$ docker exec -ti vitamin-station-app /bin/sh

# inside the container
$ npm run db:create src/migrations/<migration-name>
```

For running the migrations, run the following command:

```bash
# enter the container (if not already)
$ docker exec -ti vitamin-station-app /bin/sh

# inside the container
$ npm run db:migrate
```

## API Features

### Groups

The API provides endpoints for managing groups. A group is a collection of users managed by a trainer, allowing for organized training sessions and plan sharing.

#### Endpoints

- `POST /api/v1/group` - Create a new group
- `GET /api/v1/group` - Get all groups for the current user
- `GET /api/v1/group/{group_id}` - Get details of a specific group
- `PUT /api/v1/group/{group_id}` - Update a group's details
- `POST /api/v1/group/{group_id}/user/{user_id}` - Add a user to a group
- `DELETE /api/v1/group/{group_id}/user/{user_id}` - Remove a user from a group

#### Group Structure

A group consists of:

- A name and optional description
- Optional tags for categorization
- An invitation URL for adding new members
- A list of users who are members of the group

The invitation URL contains a secure JWT token that encodes the group ID, allowing users to join the group by scanning a QR code.

### Training Plans

The API provides endpoints for managing training plans. A training plan is a collection of trainings arranged in a specific order, representing a workout schedule.

#### Endpoints

- `POST /api/v1/training-plan` - Create a new training plan
- `GET /api/v1/training-plan?status=active|deleted` - Get all training plans
- `GET /api/v1/training-plan/{training_plan_id}` - Get a specific training plan
- `PUT /api/v1/training-plan/{training_plan_id}` - Update a training plan
- `DELETE /api/v1/training-plan/{training_plan_id}` - Delete a training plan (soft delete)

#### Training Plan Structure

A training plan consists of:

- A name and description
- An ordered list of trainings
- Status (active or deleted)

The order of trainings in the array represents the sequence of workout days. Trainers can rearrange the order of trainings using drag-and-drop in the mobile application.

### Training Plan Group Assignment

The API provides endpoints for assigning training plans to groups. This allows trainers to share training plans with multiple users at once.

#### Endpoints

- `POST /api/v1/training-plan/{training_plan_id}/group/assign/{group_id}` - Assign a training plan to a group with a start date
- `DELETE /api/v1/training-plan/{training_plan_id}/group/unassign/{group_id}` - Unassign a training plan from a group

#### Assignment Structure

A training plan assignment consists of:

- A training plan ID
- A group ID
- A start date (when the group should begin the training plan)

When a training plan is assigned to a group, all members of the group will have access to the training plan starting from the specified start date.

For detailed API documentation, refer to the Swagger documentation available at `/api/docs` when the server is running.

### Testing Users Creds

The application comes with pre-seeded test users for development and testing purposes:

| Email               | Password     | Name    | Roles                    |
| ------------------- | ------------ | ------- | ------------------------ |
| <EMAIL>     | Aa123456789! | admin   | SUPER_ADMIN              |
| <EMAIL>       | zaq1@WSX     | user    | USER                     |
| <EMAIL> | zaq1@WSX     | trainer | USER, TRAINER            |
| <EMAIL>       | zaq1@WSX     | diet    | USER, DIETITIAN          |
| <EMAIL>         | zaq1@WSX     | all     | USER, DIETITIAN, TRAINER |

**Note:** These credentials are for development/testing purposes only and should not be used in production environments.
