import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { config } from '@app/ormconfig';
import { UsersModule } from '@app/users/users.module';
import { AppController } from '@app/app.controller';
import { AppService } from '@app/app.service';
import { LoggerModule } from '@app/configs/logger/logger.module';
import { AuthModule } from './auth/auth.module';
import { ExercisesModule } from './exercises/exercises.module';
import { ConsentsModule } from './consents/consents.module';
import { TrainingModule } from './training/training.module';
import { TrainingPlanModule } from './training-plan/training-plan.module';
import { GroupsModule } from './groups/groups.module';
import { MailModule } from './mail/mail.module';
import { CqrsModule } from '@nestjs/cqrs';
import { SeedModule } from '@app/seed/seed.module';

@Module({
  imports: [
    LoggerModule,
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({ ...config, autoLoadEntities: true }),
    CqrsModule.forRoot(),
    UsersModule,
    AuthModule,
    ExercisesModule,
    TrainingModule,
    TrainingPlanModule,
    ConsentsModule,
    GroupsModule,
    MailModule,
    SeedModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
