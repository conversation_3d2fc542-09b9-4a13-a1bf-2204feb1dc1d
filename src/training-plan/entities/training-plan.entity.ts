import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
  OneToMany,
} from 'typeorm';
import { UserEntity } from '@app/users/entities/user.entity';
import { ApiProperty } from '@nestjs/swagger';
import { TrainingPlanStatus } from '../enums/training-plan-status.enum';
import { TrainingEntity } from '@app/training/entities/training.entity';
import { Exclude } from 'class-transformer';
import { TrainingPlanGroupEntity } from './training-plan-group.entity';

@Entity({ name: 'training_plans' })
export class TrainingPlanEntity {
  @ApiProperty({
    description: 'Unique training plan identifier',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Training plan name',
    example: 'Plan na tydzień',
  })
  @Column()
  name: string;

  @ApiProperty({
    description: 'Training plan description',
    example: 'Super plan treningowy',
  })
  @Column({ type: 'text' })
  description: string;

  @ApiProperty({
    description: 'Training plan status',
    enum: TrainingPlanStatus,
    example: TrainingPlanStatus.ACTIVE,
  })
  @Column({
    type: 'enum',
    enum: TrainingPlanStatus,
    default: TrainingPlanStatus.ACTIVE,
  })
  status: TrainingPlanStatus;

  @ApiProperty({
    description: 'Training plan creation timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'Training plan last update timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({
    description: 'Creator of the training plan',
    type: () => UserEntity,
  })
  @ManyToOne(() => UserEntity, (user) => 'trainingPlans', { eager: true })
  @Exclude()
  creator: UserEntity;

  @Column()
  creatorId: string;

  @ApiProperty({
    description: 'Trainings included in the training plan',
    type: () => [TrainingEntity],
  })
  @ManyToMany(() => TrainingEntity)
  @JoinTable({
    name: 'training_plan_trainings',
    joinColumn: {
      name: 'training_plan_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'training_id',
      referencedColumnName: 'id',
    },
  })
  trainings: TrainingEntity[];

  @ApiProperty({
    description: 'Groups assigned to this training plan',
    type: () => [TrainingPlanGroupEntity],
  })
  @OneToMany(() => TrainingPlanGroupEntity, (trainingPlanGroup) => trainingPlanGroup.trainingPlan)
  trainingPlanGroups: TrainingPlanGroupEntity[];
}
