import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { TrainingPlanEntity } from '../entities/training-plan.entity';
import { CreateTrainingPlanDto } from '../dto/create-training-plan.dto';
import { UpdateTrainingPlanDto } from '../dto/update-training-plan.dto';
import { UserEntity } from '@app/users/entities/user.entity';
import { TrainingPlanStatus } from '../enums/training-plan-status.enum';
import { TrainingEntity } from '@app/training/entities/training.entity';
import { GroupEntity } from '@app/groups/entities/group.entity';
import { TrainingPlanGroupEntity } from '../entities/training-plan-group.entity';
import { AssignTrainingPlanToGroupDto } from '../dto/assign-training-plan-to-group.dto';
import { TrainingPlanGroupResponseDto } from '../dto/training-plan-group-response.dto';

@Injectable()
export class TrainingPlanService {
  constructor(
    @InjectRepository(TrainingPlanEntity)
    private trainingPlanRepository: Repository<TrainingPlanEntity>,
    @InjectRepository(TrainingEntity)
    private trainingRepository: Repository<TrainingEntity>,
    @InjectRepository(GroupEntity)
    private groupRepository: Repository<GroupEntity>,
    @InjectRepository(TrainingPlanGroupEntity)
    private trainingPlanGroupRepository: Repository<TrainingPlanGroupEntity>,
  ) {}

  /**
   * Filter profanity from text
   * @param text Text to filter
   * @returns Filtered text
   */
  private filterProfanity(text: string): string {
    // Simple profanity filter - in a real app, you'd use a more comprehensive library
    const profanityList = ['brzydkie', 'słowo', 'wulgarny'];
    let filteredText = text;

    profanityList.forEach((word) => {
      const regex = new RegExp(word, 'gi');
      filteredText = filteredText.replace(regex, '&**&');
    });

    return filteredText;
  }

  /**
   * Creates a new training plan for a user
   * @param createTrainingPlanDto Data for creating the training plan
   * @param user The user creating the training plan
   * @returns The created training plan
   */
  async createTrainingPlan(
    createTrainingPlanDto: CreateTrainingPlanDto,
    user: UserEntity,
  ): Promise<TrainingPlanEntity> {
    try {
      // Filter profanity from name and description
      const filteredName = this.filterProfanity(createTrainingPlanDto.name);
      const filteredDescription = this.filterProfanity(createTrainingPlanDto.description);

      // Verify all trainings exist and belong to the user
      const trainings = await this.trainingRepository.find({
        where: {
          id: In(createTrainingPlanDto.trainings || []),
          creatorId: user.id,
        },
      });

      if (trainings.length !== (createTrainingPlanDto.trainings || []).length) {
        throw new BadRequestException('One or more trainings do not exist or do not belong to you');
      }

      const trainingPlan = this.trainingPlanRepository.create({
        name: filteredName,
        description: filteredDescription,
        creator: user,
        creatorId: user.id,
        status: TrainingPlanStatus.ACTIVE,
        trainings: trainings,
      });

      return await this.trainingPlanRepository.save(trainingPlan);
    } catch (error) {
      throw new BadRequestException('Failed to create training plan: ' + error.message);
    }
  }

  /**
   * Finds all training plans for a user with optional status filtering
   * @param user The user whose training plans to find
   * @param status Optional status filter
   * @returns Array of training plans
   */
  async findAll(user: UserEntity, status?: TrainingPlanStatus): Promise<TrainingPlanEntity[]> {
    try {
      const query = this.trainingPlanRepository
        .createQueryBuilder('trainingPlan')
        .leftJoinAndSelect('trainingPlan.trainings', 'trainings')
        .where('trainingPlan.creatorId = :creatorId', { creatorId: user.id });

      if (status) {
        query.andWhere('trainingPlan.status = :status', { status });
      }

      return await query.getMany();
    } catch (error) {
      throw new BadRequestException('Failed to retrieve training plans: ' + error.message);
    }
  }

  /**
   * Finds a specific training plan by ID
   * @param id The training plan ID
   * @param user The user requesting the training plan
   * @returns The found training plan
   */
  async findOne(id: string, user: UserEntity): Promise<TrainingPlanEntity> {
    if (!id) {
      throw new BadRequestException('Training plan ID is required');
    }

    const trainingPlan = await this.trainingPlanRepository.findOne({
      where: { id },
      relations: ['trainings'],
    });

    if (!trainingPlan) {
      throw new NotFoundException(`Training plan with ID ${id} not found`);
    }

    if (trainingPlan.creatorId !== user.id) {
      throw new ForbiddenException('You do not have permission to access this training plan');
    }

    // Format the response to match the expected format
    return trainingPlan;
  }

  /**
   * Updates an existing training plan
   * @param id The training plan ID
   * @param updateTrainingPlanDto The update data
   * @param user The user updating the training plan
   * @returns The updated training plan
   */
  async update(
    id: string,
    updateTrainingPlanDto: UpdateTrainingPlanDto,
    user: UserEntity,
  ): Promise<TrainingPlanEntity> {
    // First verify the training plan exists and user has permission
    await this.findOne(id, user);

    try {
      // Get the original entity from repository
      const trainingPlan = await this.trainingPlanRepository.findOne({
        where: { id },
        relations: ['trainings'],
      });

      if (!trainingPlan) {
        throw new NotFoundException(`Training plan with ID ${id} not found`);
      }

      // Filter profanity if fields are provided
      if (updateTrainingPlanDto.name) {
        updateTrainingPlanDto.name = this.filterProfanity(updateTrainingPlanDto.name);
      }

      if (updateTrainingPlanDto.description) {
        updateTrainingPlanDto.description = this.filterProfanity(updateTrainingPlanDto.description);
      }

      // Update trainings if provided
      if (updateTrainingPlanDto.trainings) {
        const trainings = await this.trainingRepository.find({
          where: {
            id: In(updateTrainingPlanDto.trainings || []),
            creatorId: user.id,
          },
        });

        if (trainings.length !== (updateTrainingPlanDto.trainings || []).length) {
          throw new BadRequestException('One or more trainings do not exist or do not belong to you');
        }

        trainingPlan.trainings = trainings;
      }

      // Update other fields
      if (updateTrainingPlanDto.name) trainingPlan.name = updateTrainingPlanDto.name;
      if (updateTrainingPlanDto.description) trainingPlan.description = updateTrainingPlanDto.description;

      // Save the updated training plan
      return await this.trainingPlanRepository.save(trainingPlan);
    } catch (error) {
      throw new BadRequestException('Failed to update training plan: ' + error.message);
    }
  }

  /**
   * Soft deletes a training plan by setting its status to DELETED
   * @param id The training plan ID
   * @param user The user deleting the training plan
   * @returns Object with status
   */
  async remove(id: string, user: UserEntity): Promise<{ status: TrainingPlanStatus }> {
    // First verify the training plan exists and user has permission
    await this.findOne(id, user);

    try {
      // Set status to deleted instead of actually removing
      await this.trainingPlanRepository.update(id, { status: TrainingPlanStatus.DELETED });

      return {
        status: TrainingPlanStatus.DELETED,
      };
    } catch (error) {
      throw new BadRequestException('Failed to delete training plan: ' + error.message);
    }
  }

  /**
   * Assigns a training plan to a group with a specified start date
   * @param trainingPlanId The training plan ID
   * @param groupId The group ID
   * @param assignDto The assignment data containing start date
   * @param user The user making the assignment
   * @returns The created training plan group relationship
   */
  async assignToGroup(
    trainingPlanId: string,
    groupId: string,
    assignDto: AssignTrainingPlanToGroupDto,
    user: UserEntity,
  ): Promise<TrainingPlanGroupResponseDto> {
    // Verify the training plan exists and user has permission
    await this.findOne(trainingPlanId, user);

    // Verify the group exists and user has permission
    const group = await this.groupRepository.findOne({
      where: { id: groupId },
    });

    if (!group) {
      throw new NotFoundException(`Group with ID ${groupId} not found`);
    }

    if (group.ownerId !== user.id) {
      throw new ForbiddenException('You do not have permission to assign training plans to this group');
    }

    // Check if the training plan is already assigned to the group
    const existingAssignment = await this.trainingPlanGroupRepository
      .createQueryBuilder('tpg')
      .where('tpg.training_plan_id = :trainingPlanId', { trainingPlanId })
      .andWhere('tpg.group_id = :groupId', { groupId })
      .getOne();

    if (existingAssignment) {
      throw new ConflictException('This training plan is already assigned to the group');
    }

    try {
      // Create the new assignment
      const trainingPlanGroup = this.trainingPlanGroupRepository.create({
        trainingPlanId,
        groupId,
        startDate: new Date(assignDto.startDate),
      });

      const savedAssignment = await this.trainingPlanGroupRepository.save(trainingPlanGroup);

      // Format the response
      return {
        id: savedAssignment.id,
        trainingPlanId: savedAssignment.trainingPlanId,
        groupId: savedAssignment.groupId,
        startDate: savedAssignment.startDate.toISOString().split('T')[0], // Format as YYYY-MM-DD
        createdAt: savedAssignment.createdAt,
      };
    } catch (error) {
      throw new BadRequestException('Failed to assign training plan to group: ' + error.message);
    }
  }

  /**
   * Unassigns a training plan from a group
   * @param trainingPlanId The training plan ID
   * @param groupId The group ID
   * @param user The user making the unassignment
   */
  async unassignFromGroup(trainingPlanId: string, groupId: string, user: UserEntity): Promise<void> {
    // Verify the training plan exists and user has permission
    await this.findOne(trainingPlanId, user);

    // Verify the group exists and user has permission
    const group = await this.groupRepository.findOne({
      where: { id: groupId },
    });

    if (!group) {
      throw new NotFoundException(`Group with ID ${groupId} not found`);
    }

    if (group.ownerId !== user.id) {
      throw new ForbiddenException('You do not have permission to unassign training plans from this group');
    }

    // Check if the assignment exists
    const existingAssignment = await this.trainingPlanGroupRepository
      .createQueryBuilder('tpg')
      .where('tpg.training_plan_id = :trainingPlanId', { trainingPlanId })
      .andWhere('tpg.group_id = :groupId', { groupId })
      .getOne();

    if (!existingAssignment) {
      throw new NotFoundException('This training plan is not assigned to the specified group');
    }

    try {
      // Remove the assignment
      await this.trainingPlanGroupRepository.remove(existingAssignment);
    } catch (error) {
      throw new BadRequestException('Failed to unassign training plan from group: ' + error.message);
    }
  }
}
