import { Test, TestingModule } from '@nestjs/testing';
import { TrainingPlanService } from './training-plan.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { TrainingPlanEntity } from '../entities/training-plan.entity';
import { TrainingEntity } from '@app/training/entities/training.entity';
import { Repository, In } from 'typeorm';
import { CreateTrainingPlanDto } from '../dto/create-training-plan.dto';
import { UserEntity } from '@app/users/entities/user.entity';
import { TrainingPlanStatus } from '../enums/training-plan-status.enum';
import { BadRequestException, ConflictException, ForbiddenException, NotFoundException } from '@nestjs/common';
import { UpdateTrainingPlanDto } from '../dto/update-training-plan.dto';
import { GroupEntity } from '@app/groups/entities/group.entity';
import { TrainingPlanGroupEntity } from '../entities/training-plan-group.entity';
import { AssignTrainingPlanToGroupDto } from '../dto/assign-training-plan-to-group.dto';

describe('TrainingPlanService', () => {
  let service: TrainingPlanService;
  let trainingPlanRepository: Repository<TrainingPlanEntity>;
  let trainingRepository: Repository<TrainingEntity>;
  let groupRepository: Repository<GroupEntity>;
  let trainingPlanGroupRepository: Repository<TrainingPlanGroupEntity>;

  const mockUser = {
    id: 'user-id',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'hashedPassword',
    consents: ['RODO'],
    roles: ['trainer'],
    createdAt: new Date(),
  } as unknown as UserEntity;

  const mockTrainings = [
    { id: 'training_id_1', name: 'Training 1', creatorId: 'user-id' },
    { id: 'training_id_2', name: 'Training 2', creatorId: 'user-id' },
  ] as TrainingEntity[];

  const mockTrainingPlan = {
    id: 'training_plan_id',
    name: 'Plan na tydzień',
    description: 'Super plan treningowy',
    trainings: mockTrainings,
    status: TrainingPlanStatus.ACTIVE,
    creatorId: 'user-id',
    creator: mockUser,
    createdAt: new Date(),
    updatedAt: new Date(),
  } as TrainingPlanEntity;

  const mockGroup: GroupEntity = {
    id: 'group_id',
    name: 'Test Group',
    description: 'Test group description',
    ownerId: 'user-id',
    invitationUrl: 'https://invite-to-group.pl/token',
    trainingPlanGroups: [],
    groupUsers: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockTrainingPlanGroup = {
    id: 'training_plan_group_id',
    trainingPlanId: 'training_plan_id',
    groupId: 'group_id',
    startDate: new Date('2025-05-06'),
    createdAt: new Date(),
    trainingPlan: mockTrainingPlan,
    group: mockGroup,
  } as TrainingPlanGroupEntity;

  // Create a mock query builder that we can reference directly in tests
  const mockQueryBuilder = {
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    getMany: jest.fn().mockResolvedValue([]),
    getOne: jest.fn().mockResolvedValue(null),
  };

  const mockTrainingPlanRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    createQueryBuilder: jest.fn(() => mockQueryBuilder),
  };

  const mockTrainingRepository = {
    find: jest.fn(),
  };

  const mockGroupRepository = {
    findOne: jest.fn(),
  };

  const mockTrainingPlanGroupRepository = {
    create: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
    createQueryBuilder: jest.fn(() => mockQueryBuilder),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TrainingPlanService,
        {
          provide: getRepositoryToken(TrainingPlanEntity),
          useValue: mockTrainingPlanRepository,
        },
        {
          provide: getRepositoryToken(TrainingEntity),
          useValue: mockTrainingRepository,
        },
        {
          provide: getRepositoryToken(GroupEntity),
          useValue: mockGroupRepository,
        },
        {
          provide: getRepositoryToken(TrainingPlanGroupEntity),
          useValue: mockTrainingPlanGroupRepository,
        },
      ],
    }).compile();

    service = module.get<TrainingPlanService>(TrainingPlanService);
    trainingPlanRepository = module.get<Repository<TrainingPlanEntity>>(getRepositoryToken(TrainingPlanEntity));
    trainingRepository = module.get<Repository<TrainingEntity>>(getRepositoryToken(TrainingEntity));
    groupRepository = module.get<Repository<GroupEntity>>(getRepositoryToken(GroupEntity));
    trainingPlanGroupRepository = module.get<Repository<TrainingPlanGroupEntity>>(
      getRepositoryToken(TrainingPlanGroupEntity),
    );

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('filterProfanity', () => {
    it('should filter out profanity words', () => {
      // Using the private method through any type casting
      const result = (service as any).filterProfanity('To jest brzydkie słowo i wulgarny tekst');
      expect(result).toBe('To jest &**& &**& i &**& tekst');
    });

    it('should return the original text if no profanity is found', () => {
      const result = (service as any).filterProfanity('To jest normalny tekst');
      expect(result).toBe('To jest normalny tekst');
    });

    it('should handle case insensitivity', () => {
      const result = (service as any).filterProfanity('To jest BRZYDKIE słowo i WuLgArNy tekst');
      expect(result).toBe('To jest &**& &**& i &**& tekst');
    });

    it('should handle multiple occurrences of the same profanity word', () => {
      const result = (service as any).filterProfanity('brzydkie brzydkie brzydkie');
      expect(result).toBe('&**& &**& &**&');
    });

    it('should handle empty string', () => {
      const result = (service as any).filterProfanity('');
      expect(result).toBe('');
    });
  });

  describe('createTrainingPlan', () => {
    it('should create a training plan successfully', async () => {
      const createTrainingPlanDto: CreateTrainingPlanDto = {
        name: 'Plan na tydzień',
        description: 'Super plan treningowy',
        trainings: mockTrainings.map((t) => t.id),
      };

      mockTrainingRepository.find.mockResolvedValue(mockTrainings);
      mockTrainingPlanRepository.create.mockReturnValue(mockTrainingPlan);
      mockTrainingPlanRepository.save.mockResolvedValue(mockTrainingPlan);

      const result = await service.createTrainingPlan(createTrainingPlanDto, mockUser);

      expect(mockTrainingRepository.find).toHaveBeenCalledWith({
        where: {
          id: In(createTrainingPlanDto.trainings),
          creatorId: mockUser.id,
        },
      });
      expect(mockTrainingPlanRepository.create).toHaveBeenCalled();
      expect(mockTrainingPlanRepository.save).toHaveBeenCalled();
      expect(result).toHaveProperty('id');
    });

    it('should filter profanity in name and description', async () => {
      const createTrainingPlanDto: CreateTrainingPlanDto = {
        name: 'Plan z brzydkie słowo',
        description: 'Opis z wulgarny tekst',
        trainings: mockTrainings.map((t) => t.id),
      };

      mockTrainingRepository.find.mockResolvedValue(mockTrainings);

      // Capture the created object to verify filtered content
      mockTrainingPlanRepository.create.mockImplementation((dto) => ({
        ...mockTrainingPlan,
        name: dto.name,
        description: dto.description,
      }));

      mockTrainingPlanRepository.save.mockImplementation((entity) => entity);

      await service.createTrainingPlan(createTrainingPlanDto, mockUser);

      expect(mockTrainingPlanRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Plan z &**& &**&',
          description: 'Opis z &**& tekst',
        }),
      );
    });

    it('should throw BadRequestException if trainings do not exist or do not belong to user', async () => {
      const createTrainingPlanDto: CreateTrainingPlanDto = {
        name: 'Plan na tydzień',
        description: 'Super plan treningowy',
        trainings: mockTrainings.map((t) => t.id),
      };

      // Return only one training to simulate missing training
      mockTrainingRepository.find.mockResolvedValue([mockTrainings[0]]);

      await expect(service.createTrainingPlan(createTrainingPlanDto, mockUser)).rejects.toThrow(BadRequestException);
    });

    it('should handle errors during creation', async () => {
      const createTrainingPlanDto: CreateTrainingPlanDto = {
        name: 'Plan na tydzień',
        description: 'Super plan treningowy',
        trainings: mockTrainings.map((t) => t.id),
      };

      mockTrainingRepository.find.mockResolvedValue(mockTrainings);
      mockTrainingPlanRepository.create.mockReturnValue(mockTrainingPlan);
      mockTrainingPlanRepository.save.mockRejectedValue(new Error('Database error'));

      await expect(service.createTrainingPlan(createTrainingPlanDto, mockUser)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findAll', () => {
    beforeEach(() => {
      // Reset mock calls before each test
      mockQueryBuilder.where.mockClear();
      mockQueryBuilder.andWhere.mockClear();
      mockQueryBuilder.getMany.mockClear();
    });

    it('should return all training plans for a user', async () => {
      mockQueryBuilder.getMany.mockResolvedValue([mockTrainingPlan]);

      const result = await service.findAll(mockUser);

      expect(mockQueryBuilder.where).toHaveBeenCalledWith('trainingPlan.creatorId = :creatorId', {
        creatorId: mockUser.id,
      });
      expect(result).toEqual([mockTrainingPlan]);
    });

    it('should filter training plans by status', async () => {
      mockQueryBuilder.getMany.mockResolvedValue([mockTrainingPlan]);

      const result = await service.findAll(mockUser, TrainingPlanStatus.ACTIVE);

      expect(mockQueryBuilder.where).toHaveBeenCalledWith('trainingPlan.creatorId = :creatorId', {
        creatorId: mockUser.id,
      });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('trainingPlan.status = :status', {
        status: TrainingPlanStatus.ACTIVE,
      });
      expect(result).toEqual([mockTrainingPlan]);
    });

    it('should handle errors during findAll', async () => {
      mockQueryBuilder.getMany.mockRejectedValue(new Error('Database error'));

      await expect(service.findAll(mockUser)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findOne', () => {
    it('should return a training plan by id', async () => {
      mockTrainingPlanRepository.findOne.mockResolvedValue(mockTrainingPlan);

      const result = await service.findOne('training_plan_id', mockUser);

      expect(mockTrainingPlanRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'training_plan_id' },
        relations: ['trainings'],
      });
      expect(result).toEqual(mockTrainingPlan);
    });

    it('should throw NotFoundException if training plan not found', async () => {
      mockTrainingPlanRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('non-existent-id', mockUser)).rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException if user does not own the training plan', async () => {
      const otherUserTrainingPlan = { ...mockTrainingPlan, creatorId: 'other-user-id' };
      mockTrainingPlanRepository.findOne.mockResolvedValue(otherUserTrainingPlan);

      await expect(service.findOne('training_plan_id', mockUser)).rejects.toThrow(ForbiddenException);
    });
  });

  describe('update', () => {
    beforeEach(() => {
      // Reset mock calls before each test
      mockTrainingPlanRepository.findOne.mockClear();
      mockTrainingPlanRepository.save.mockClear();
      mockTrainingRepository.find.mockClear();
    });

    it('should update a training plan successfully with name only', async () => {
      const updateTrainingPlanDto: UpdateTrainingPlanDto = {
        name: 'Updated Training Plan Name',
      };

      mockTrainingPlanRepository.findOne
        .mockResolvedValueOnce(mockTrainingPlan) // For permission check
        .mockResolvedValueOnce(mockTrainingPlan); // For actual update

      const updatedTrainingPlan = {
        ...mockTrainingPlan,
        name: 'Updated Training Plan Name',
      };
      mockTrainingPlanRepository.save.mockResolvedValue(updatedTrainingPlan);

      const result = await service.update('training_plan_id', updateTrainingPlanDto, mockUser);

      // Verify findOne was called exactly twice
      expect(mockTrainingPlanRepository.findOne).toHaveBeenCalledTimes(2);
      expect(mockTrainingPlanRepository.save).toHaveBeenCalled();
      expect(result.name).toBe('Updated Training Plan Name');
    });

    it('should update a training plan successfully with description only', async () => {
      const updateTrainingPlanDto: UpdateTrainingPlanDto = {
        description: 'Updated description',
      };

      mockTrainingPlanRepository.findOne
        .mockResolvedValueOnce(mockTrainingPlan) // For permission check
        .mockResolvedValueOnce(mockTrainingPlan); // For actual update

      const updatedTrainingPlan = {
        ...mockTrainingPlan,
        description: 'Updated description',
      };
      mockTrainingPlanRepository.save.mockResolvedValue(updatedTrainingPlan);

      const result = await service.update('training_plan_id', updateTrainingPlanDto, mockUser);

      expect(result.description).toBe('Updated description');
    });

    it('should update a training plan successfully with trainings only', async () => {
      const updateTrainingPlanDto: UpdateTrainingPlanDto = {
        trainings: ['training_id_1', 'training_id_2'],
      };

      mockTrainingPlanRepository.findOne
        .mockResolvedValueOnce(mockTrainingPlan) // For permission check
        .mockResolvedValueOnce(mockTrainingPlan); // For actual update

      mockTrainingRepository.find.mockResolvedValue(mockTrainings);

      const updatedTrainingPlan = {
        ...mockTrainingPlan,
        trainings: mockTrainings,
      };
      mockTrainingPlanRepository.save.mockResolvedValue(updatedTrainingPlan);

      const result = await service.update('training_plan_id', updateTrainingPlanDto, mockUser);

      expect(mockTrainingRepository.find).toHaveBeenCalledWith({
        where: {
          id: In(updateTrainingPlanDto.trainings || []),
          creatorId: mockUser.id,
        },
      });
      expect(result.trainings).toEqual(mockTrainings);
    });

    it('should filter profanity in updated name and description', async () => {
      const updateTrainingPlanDto: UpdateTrainingPlanDto = {
        name: 'Plan z brzydkie słowo',
        description: 'Opis z wulgarny tekst',
      };

      mockTrainingPlanRepository.findOne
        .mockResolvedValueOnce(mockTrainingPlan) // For permission check
        .mockResolvedValueOnce(mockTrainingPlan); // For actual update

      // Capture the saved object to verify filtered content
      mockTrainingPlanRepository.save.mockImplementation((entity) => entity);

      await service.update('training_plan_id', updateTrainingPlanDto, mockUser);

      expect(mockTrainingPlanRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Plan z &**& &**&',
          description: 'Opis z &**& tekst',
        }),
      );
    });

    it('should throw BadRequestException if trainings do not exist or do not belong to user', async () => {
      const updateTrainingPlanDto: UpdateTrainingPlanDto = {
        trainings: ['training_id_1', 'training_id_2'],
      };

      mockTrainingPlanRepository.findOne
        .mockResolvedValueOnce(mockTrainingPlan) // For permission check
        .mockResolvedValueOnce(mockTrainingPlan); // For actual update

      // Return only one training to simulate missing training
      mockTrainingRepository.find.mockResolvedValue([mockTrainings[0]]);

      await expect(service.update('training_plan_id', updateTrainingPlanDto, mockUser)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should handle errors during update', async () => {
      const updateTrainingPlanDto: UpdateTrainingPlanDto = {
        name: 'Updated Training Plan Name',
      };

      mockTrainingPlanRepository.findOne
        .mockResolvedValueOnce(mockTrainingPlan) // For permission check
        .mockResolvedValueOnce(mockTrainingPlan); // For actual update

      mockTrainingPlanRepository.save.mockRejectedValue(new Error('Database error'));

      await expect(service.update('training_plan_id', updateTrainingPlanDto, mockUser)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('remove', () => {
    it('should soft delete a training plan', async () => {
      mockTrainingPlanRepository.findOne.mockResolvedValue(mockTrainingPlan);

      const result = await service.remove('training_plan_id', mockUser);

      expect(mockTrainingPlanRepository.update).toHaveBeenCalledWith('training_plan_id', {
        status: TrainingPlanStatus.DELETED,
      });
      expect(result).toEqual({ status: TrainingPlanStatus.DELETED });
    });

    it('should handle errors during removal', async () => {
      mockTrainingPlanRepository.findOne.mockResolvedValue(mockTrainingPlan);
      mockTrainingPlanRepository.update.mockRejectedValue(new Error('Database error'));

      await expect(service.remove('training_plan_id', mockUser)).rejects.toThrow(BadRequestException);
    });
  });

  describe('assignToGroup', () => {
    const assignDto: AssignTrainingPlanToGroupDto = {
      startDate: '2025-05-06',
    };

    beforeEach(() => {
      // Setup default mocks for the happy path
      mockTrainingPlanRepository.findOne.mockResolvedValue(mockTrainingPlan);
      mockGroupRepository.findOne.mockResolvedValue(mockGroup);
      mockQueryBuilder.getOne.mockResolvedValue(null); // No existing assignment
      mockTrainingPlanGroupRepository.create.mockReturnValue(mockTrainingPlanGroup);
      mockTrainingPlanGroupRepository.save.mockResolvedValue(mockTrainingPlanGroup);
    });

    it('should assign a training plan to a group successfully', async () => {
      const result = await service.assignToGroup('training_plan_id', 'group_id', assignDto, mockUser);

      expect(mockTrainingPlanRepository.findOne).toHaveBeenCalled();
      expect(mockGroupRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'group_id' },
      });
      expect(mockTrainingPlanGroupRepository.create).toHaveBeenCalledWith({
        trainingPlanId: 'training_plan_id',
        groupId: 'group_id',
        startDate: new Date(assignDto.startDate),
      });
      expect(mockTrainingPlanGroupRepository.save).toHaveBeenCalled();
      expect(result).toEqual({
        id: mockTrainingPlanGroup.id,
        trainingPlanId: mockTrainingPlanGroup.trainingPlanId,
        groupId: mockTrainingPlanGroup.groupId,
        startDate: mockTrainingPlanGroup.startDate.toISOString().split('T')[0],
        createdAt: mockTrainingPlanGroup.createdAt,
      });
    });

    it('should throw NotFoundException if group does not exist', async () => {
      mockGroupRepository.findOne.mockResolvedValue(null);

      await expect(service.assignToGroup('training_plan_id', 'group_id', assignDto, mockUser)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw ForbiddenException if user does not own the group', async () => {
      const otherUserGroup = { ...mockGroup, ownerId: 'other-user-id' };
      mockGroupRepository.findOne.mockResolvedValue(otherUserGroup);

      await expect(service.assignToGroup('training_plan_id', 'group_id', assignDto, mockUser)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should throw ConflictException if training plan is already assigned to the group', async () => {
      mockQueryBuilder.getOne.mockResolvedValue(mockTrainingPlanGroup); // Existing assignment

      await expect(service.assignToGroup('training_plan_id', 'group_id', assignDto, mockUser)).rejects.toThrow(
        ConflictException,
      );
    });

    it('should handle errors during assignment', async () => {
      mockTrainingPlanGroupRepository.save.mockRejectedValue(new Error('Database error'));

      await expect(service.assignToGroup('training_plan_id', 'group_id', assignDto, mockUser)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('unassignFromGroup', () => {
    beforeEach(() => {
      // Setup default mocks for the happy path
      mockTrainingPlanRepository.findOne.mockResolvedValue(mockTrainingPlan);
      mockGroupRepository.findOne.mockResolvedValue(mockGroup);
      mockQueryBuilder.getOne.mockResolvedValue(mockTrainingPlanGroup); // Existing assignment
      mockTrainingPlanGroupRepository.remove.mockResolvedValue(undefined);
    });

    it('should unassign a training plan from a group successfully', async () => {
      await service.unassignFromGroup('training_plan_id', 'group_id', mockUser);

      expect(mockTrainingPlanRepository.findOne).toHaveBeenCalled();
      expect(mockGroupRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'group_id' },
      });
      expect(mockTrainingPlanGroupRepository.remove).toHaveBeenCalledWith(mockTrainingPlanGroup);
    });

    it('should throw NotFoundException if group does not exist', async () => {
      mockGroupRepository.findOne.mockResolvedValue(null);

      await expect(service.unassignFromGroup('training_plan_id', 'group_id', mockUser)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw ForbiddenException if user does not own the group', async () => {
      const otherUserGroup = { ...mockGroup, ownerId: 'other-user-id' };
      mockGroupRepository.findOne.mockResolvedValue(otherUserGroup);

      await expect(service.unassignFromGroup('training_plan_id', 'group_id', mockUser)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should throw NotFoundException if training plan is not assigned to the group', async () => {
      mockQueryBuilder.getOne.mockResolvedValue(null); // No existing assignment

      await expect(service.unassignFromGroup('training_plan_id', 'group_id', mockUser)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should handle errors during unassignment', async () => {
      mockTrainingPlanGroupRepository.remove.mockRejectedValue(new Error('Database error'));

      await expect(service.unassignFromGroup('training_plan_id', 'group_id', mockUser)).rejects.toThrow(
        BadRequestException,
      );
    });
  });
});
