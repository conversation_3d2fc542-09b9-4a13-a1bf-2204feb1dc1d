import { Test, TestingModule } from '@nestjs/testing';
import { ExercisesService } from './exercises.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ExerciseEntity } from '../entities/exercise.entity';
import { Repository } from 'typeorm';
import { CreateExerciseDto } from '../dto/create-exercise.dto';
import { UserEntity } from '@app/users/entities/user.entity';
import { ExerciseStatus } from '../enums/exercise-status.enum';
import { BadRequestException, ForbiddenException, NotFoundException } from '@nestjs/common';
import { UpdateExerciseDto } from '../dto/update-exercise.dto';
import { DifficultyLevel } from '../enums/difficulty-level.enum';

describe('ExercisesService', () => {
  let service: ExercisesService;
  let exercisesRepository: Repository<ExerciseEntity>;

  const mockUser = {
    id: 'user-id',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'hashedPassword',
    consents: ['RODO'],
    roles: ['trainer'],
    createdAt: new Date(),
  } as unknown as UserEntity;

  const mockExercise: ExerciseEntity = {
    id: 'exercise_id_1',
    name: 'Pompki',
    description: 'Ćwiczenie na klatkę piersiową',
    tags: 'klatka|triceps|ramiona',
    difficultyLevel: DifficultyLevel.MEDIUM,
    videoUrl: 'https://example.com/video.mp4',
    imageUrl: 'https://example.com/image.jpg',
    status: ExerciseStatus.PENDING,
    statusMessage: 'Wymagane zgoda na przetwarzanie danych osobowych',
    creator: mockUser,
    creatorId: 'user-id',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Create a mock query builder that we can reference directly in tests
  const mockQueryBuilder = {
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    getMany: jest.fn().mockResolvedValue([]),
  };

  const mockExercisesRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    createQueryBuilder: jest.fn(() => mockQueryBuilder),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ExercisesService,
        {
          provide: getRepositoryToken(ExerciseEntity),
          useValue: mockExercisesRepository,
        },
      ],
    }).compile();

    service = module.get<ExercisesService>(ExercisesService);
    exercisesRepository = module.get<Repository<ExerciseEntity>>(getRepositoryToken(ExerciseEntity));

    // Reset mock calls before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createExercise', () => {
    it('should create an exercise successfully', async () => {
      const createExerciseDto: CreateExerciseDto = {
        name: 'Pompki',
        description: 'Ćwiczenie na klatkę piersiową',
        tags: 'klatka|triceps|ramiona',
        difficultyLevel: DifficultyLevel.MEDIUM,
        videoUrl: 'https://example.com/video.mp4',
        imageUrl: 'https://example.com/image.jpg',
        instructions: 'Wykonaj 3 serie po 10 powtórzeń',
      };

      mockExercisesRepository.create.mockReturnValue(mockExercise);
      mockExercisesRepository.save.mockResolvedValue(mockExercise);

      const result = await service.createExercise(createExerciseDto, mockUser);

      expect(mockExercisesRepository.create).toHaveBeenCalledWith({
        ...createExerciseDto,
        creator: mockUser,
        creatorId: mockUser.id,
        status: ExerciseStatus.PENDING,
      });
      expect(mockExercisesRepository.save).toHaveBeenCalled();
      expect(result).toEqual(mockExercise);
    });

    it('should throw BadRequestException if exercise creation fails', async () => {
      const createExerciseDto: CreateExerciseDto = {
        name: 'Pompki',
        description: 'Ćwiczenie na klatkę piersiową',
      };

      mockExercisesRepository.create.mockReturnValue(mockExercise);
      mockExercisesRepository.save.mockRejectedValue(new Error('Database error'));

      await expect(service.createExercise(createExerciseDto, mockUser)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findAll', () => {
    beforeEach(() => {
      // Reset mock calls before each test
      mockQueryBuilder.where.mockClear();
      mockQueryBuilder.andWhere.mockClear();
      mockQueryBuilder.getMany.mockClear();
    });

    it('should return all exercises for a user', async () => {
      mockQueryBuilder.getMany.mockResolvedValue([mockExercise]);

      const result = await service.findAll(mockUser);

      expect(mockQueryBuilder.where).toHaveBeenCalledWith('exercise.creatorId = :creatorId', {
        creatorId: mockUser.id,
      });
      expect(result).toEqual([mockExercise]);
    });

    it('should filter exercises by status', async () => {
      mockQueryBuilder.getMany.mockResolvedValue([mockExercise]);

      const result = await service.findAll(mockUser, ExerciseStatus.PENDING);

      expect(mockQueryBuilder.where).toHaveBeenCalledWith('exercise.creatorId = :creatorId', {
        creatorId: mockUser.id,
      });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('exercise.status = :status', {
        status: ExerciseStatus.PENDING,
      });
      expect(result).toEqual([mockExercise]);
    });

    it('should throw BadRequestException if query fails', async () => {
      mockQueryBuilder.getMany.mockRejectedValue(new Error('Database error'));

      await expect(service.findAll(mockUser)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findOne', () => {
    it('should return an exercise by id', async () => {
      mockExercisesRepository.findOne.mockResolvedValue(mockExercise);

      const result = await service.findOne('exercise_id_1', mockUser);

      expect(mockExercisesRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'exercise_id_1' },
      });
      expect(result).toEqual(mockExercise);
    });

    it('should throw BadRequestException if id is not provided', async () => {
      await expect(service.findOne('', mockUser)).rejects.toThrow(BadRequestException);
      await expect(service.findOne('', mockUser)).rejects.toThrow(BadRequestException);
    });

    it('should throw NotFoundException if exercise not found', async () => {
      mockExercisesRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('non-existent-id', mockUser)).rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException if user does not own the exercise', async () => {
      const otherUserExercise = { ...mockExercise, creatorId: 'other-user-id' };
      mockExercisesRepository.findOne.mockResolvedValue(otherUserExercise);

      await expect(service.findOne('exercise_id_1', mockUser)).rejects.toThrow(ForbiddenException);
    });
  });

  describe('update', () => {
    beforeEach(() => {
      // Reset mock calls before each test
      mockExercisesRepository.findOne.mockClear();
      mockExercisesRepository.save.mockClear();
    });

    it('should update an exercise successfully', async () => {
      const updateExerciseDto: UpdateExerciseDto = {
        name: 'Updated Exercise Name',
      };

      mockExercisesRepository.findOne
        .mockResolvedValueOnce(mockExercise) // For permission check
        .mockResolvedValueOnce(mockExercise); // For actual update

      const updatedExercise = {
        ...mockExercise,
        name: 'Updated Exercise Name',
        status: ExerciseStatus.PENDING, // Status should be reset to pending after update
      };
      mockExercisesRepository.save.mockResolvedValue(updatedExercise);

      const result = await service.update('exercise_id_1', updateExerciseDto, mockUser);

      // Verify findOne was called exactly twice
      expect(mockExercisesRepository.findOne).toHaveBeenCalledTimes(2);
      expect(mockExercisesRepository.save).toHaveBeenCalledWith({
        ...mockExercise,
        ...updateExerciseDto,
        status: ExerciseStatus.PENDING,
      });
      expect(result.name).toBe('Updated Exercise Name');
      expect(result.status).toBe(ExerciseStatus.PENDING);
    });

    it('should throw BadRequestException if update fails', async () => {
      const updateExerciseDto: UpdateExerciseDto = {
        name: 'Updated Exercise Name',
      };

      mockExercisesRepository.findOne
        .mockResolvedValueOnce(mockExercise) // For permission check
        .mockResolvedValueOnce(mockExercise); // For actual update

      mockExercisesRepository.save.mockRejectedValue(new Error('Database error'));

      await expect(service.update('exercise_id_1', updateExerciseDto, mockUser)).rejects.toThrow(BadRequestException);
    });
  });

  describe('remove', () => {
    it('should soft delete an exercise', async () => {
      mockExercisesRepository.findOne.mockResolvedValue(mockExercise);

      const result = await service.remove('exercise_id_1', mockUser);

      expect(mockExercisesRepository.update).toHaveBeenCalledWith('exercise_id_1', { status: ExerciseStatus.DELETED });
      expect(result).toEqual({ id: 'exercise_id_1', status: ExerciseStatus.DELETED });
    });

    it('should throw BadRequestException if delete fails', async () => {
      mockExercisesRepository.findOne.mockResolvedValue(mockExercise);
      mockExercisesRepository.update.mockRejectedValue(new Error('Database error'));

      await expect(service.remove('exercise_id_1', mockUser)).rejects.toThrow(BadRequestException);
    });
  });
});
