import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ExerciseEntity } from '../entities/exercise.entity';
import { CreateExerciseDto } from '../dto/create-exercise.dto';
import { UpdateExerciseDto } from '../dto/update-exercise.dto';
import { UserEntity } from '../../users/entities/user.entity';
import { ExerciseStatus } from '../enums/exercise-status.enum';

@Injectable()
export class ExercisesService {
  constructor(
    @InjectRepository(ExerciseEntity)
    private exercisesRepository: Repository<ExerciseEntity>,
  ) {}

  /**
   * Creates a new exercise for a user
   * @param createExerciseDto Data for creating the exercise
   * @param user The user creating the exercise
   * @returns The created exercise
   */
  async createExercise(createExerciseDto: CreateExerciseDto, user: UserEntity): Promise<ExerciseEntity> {
    try {
      const exercise = this.exercisesRepository.create({
        ...createExerciseDto,
        creator: user,
        creatorId: user.id,
        status: ExerciseStatus.PENDING,
      });

      return await this.exercisesRepository.save(exercise);
    } catch (error) {
      throw new BadRequestException('Failed to create exercise: ' + error.message);
    }
  }

  /**
   * Finds all exercises for a user with optional status filtering
   * @param status Optional status filter
   * @param user The user whose exercises to find
   * @returns Array of exercises
   */
  async findAll(user: UserEntity, status?: ExerciseStatus): Promise<ExerciseEntity[]> {
    try {
      const query = this.exercisesRepository
        .createQueryBuilder('exercise')
        .where('exercise.creatorId = :creatorId', { creatorId: user.id });

      if (status) {
        query.andWhere('exercise.status = :status', { status });
      }

      return await query.getMany();
    } catch (error) {
      throw new BadRequestException('Failed to retrieve exercises: ' + error.message);
    }
  }

  /**
   * Finds a specific exercise by ID
   * @param id The exercise ID
   * @param user The user requesting the exercise
   * @returns The found exercise
   */
  async findOne(id: string, user: UserEntity): Promise<ExerciseEntity> {
    if (!id) {
      throw new BadRequestException('Exercise ID is required');
    }

    const exercise = await this.exercisesRepository.findOne({
      where: { id },
    });

    if (!exercise) {
      throw new NotFoundException(`Exercise with ID ${id} not found`);
    }

    if (exercise.creatorId !== user.id) {
      throw new ForbiddenException('You do not have permission to access this exercise');
    }

    return exercise;
  }

  /**
   * Updates an existing exercise
   * @param id The exercise ID
   * @param updateExerciseDto The update data
   * @param user The user updating the exercise
   * @returns The updated exercise
   */
  async update(id: string, updateExerciseDto: UpdateExerciseDto, user: UserEntity): Promise<ExerciseEntity> {
    // First verify the exercise exists and user has permission
    await this.findOne(id, user);

    try {
      // Get the original entity from repository
      const exercise = await this.exercisesRepository.findOne({
        where: { id },
      });

      // Update exercise with new data
      const updatedExercise = await this.exercisesRepository.save({
        ...exercise,
        ...updateExerciseDto,
        status: ExerciseStatus.PENDING, // Reset status to pending after update
      });

      return updatedExercise;
    } catch (error) {
      throw new BadRequestException('Failed to update exercise: ' + error.message);
    }
  }

  /**
   * Soft deletes an exercise by setting its status to DELETED
   * @param id The exercise ID
   * @param user The user deleting the exercise
   * @returns Object with ID and status
   */
  async remove(id: string, user: UserEntity): Promise<{ id: string; status: ExerciseStatus }> {
    // First verify the exercise exists and user has permission
    await this.findOne(id, user);

    try {
      // Set status to deleted instead of actually removing
      await this.exercisesRepository.update(id, { status: ExerciseStatus.DELETED });

      return {
        id,
        status: ExerciseStatus.DELETED,
      };
    } catch (error) {
      throw new BadRequestException('Failed to delete exercise: ' + error.message);
    }
  }
}
