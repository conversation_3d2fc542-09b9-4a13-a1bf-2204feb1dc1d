import {
  ApiT<PERSON>s,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
  getSchemaPath,
} from '@nestjs/swagger';
import { CreateExerciseDto } from '../dto/create-exercise.dto';
import { UpdateExerciseDto } from '../dto/update-exercise.dto';
import { ExerciseStatus } from '../enums/exercise-status.enum';
import { HttpStatus, applyDecorators } from '@nestjs/common';
import { ExerciseEntity } from '../entities/exercise.entity';

export class ExercisesSwaggerDocs {
  static apiTags() {
    return applyDecorators(ApiTags('Exercises'));
  }

  static apiBearerAuth() {
    return applyDecorators(ApiBearerAuth('JWT'));
  }

  // Create exercise endpoint docs
  static createExerciseDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Create a new exercise',
        description:
          'Creates a new exercise for the authenticated trainer. The exercise will be initially set to PENDING status.',
      }),
      Api<PERSON><PERSON>({
        type: CreateExerciseDto,
        description: 'Exercise data to create',
      }),
      ApiResponse({
        status: HttpStatus.CREATED,
        description: 'The exercise has been successfully created.',
        type: ExerciseEntity,
        schema: {
          $ref: getSchemaPath(ExerciseEntity),
        },
      }),
      ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        description: 'Invalid input data provided.',
      }),
      ApiResponse({
        status: HttpStatus.UNAUTHORIZED,
        description: 'User is not authenticated.',
      }),
      ApiResponse({
        status: HttpStatus.FORBIDDEN,
        description: 'User is not a trainer or does not have permission.',
      }),
    );
  }

  // Find all exercises endpoint docs
  static findAllExercisesDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Get all exercises for the trainer',
        description: 'Retrieves all exercises created by the authenticated trainer. Can be filtered by status.',
      }),
      ApiQuery({
        name: 'status',
        required: false,
        enum: ExerciseStatus,
        description: 'Filter exercises by status (pending, verified, rejected, deleted)',
        example: ExerciseStatus.PENDING,
      }),
      ApiResponse({
        status: HttpStatus.OK,
        description: 'List of exercises',
        type: [ExerciseEntity],
        schema: {
          type: 'array',
          items: { $ref: getSchemaPath(ExerciseEntity) },
        },
      }),
      ApiResponse({
        status: HttpStatus.UNAUTHORIZED,
        description: 'User is not authenticated.',
      }),
      ApiResponse({
        status: HttpStatus.FORBIDDEN,
        description: 'User is not a trainer or does not have permission.',
      }),
    );
  }

  // Find one exercise endpoint docs
  static findOneExerciseDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Get exercise by id',
        description: 'Retrieves a specific exercise by its ID. Only accessible to the trainer who created it.',
      }),
      ApiParam({
        name: 'id',
        description: 'Exercise unique identifier (UUID)',
        type: 'string',
        example: '123e4567-e89b-12d3-a456-************',
      }),
      ApiResponse({
        status: HttpStatus.OK,
        description: 'The exercise details',
        type: ExerciseEntity,
        schema: {
          $ref: getSchemaPath(ExerciseEntity),
        },
      }),
      ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        description: 'Invalid ID format.',
      }),
      ApiResponse({
        status: HttpStatus.NOT_FOUND,
        description: 'Exercise not found.',
      }),
      ApiResponse({
        status: HttpStatus.UNAUTHORIZED,
        description: 'User is not authenticated.',
      }),
      ApiResponse({
        status: HttpStatus.FORBIDDEN,
        description: 'User is not a trainer or does not have permission to access this exercise.',
      }),
    );
  }

  // Update exercise endpoint docs
  static updateExerciseDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Update an exercise',
        description:
          'Updates an existing exercise. Only accessible to the trainer who created it. Updates will reset the status to PENDING.',
      }),
      ApiParam({
        name: 'id',
        description: 'Exercise unique identifier (UUID)',
        type: 'string',
        example: '123e4567-e89b-12d3-a456-************',
      }),
      ApiBody({
        type: UpdateExerciseDto,
        description: 'Exercise data to update',
      }),
      ApiResponse({
        status: HttpStatus.OK,
        description: 'The exercise has been successfully updated.',
        type: ExerciseEntity,
        schema: {
          $ref: getSchemaPath(ExerciseEntity),
        },
      }),
      ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        description: 'Invalid input data or ID format.',
      }),
      ApiResponse({
        status: HttpStatus.NOT_FOUND,
        description: 'Exercise not found.',
      }),
      ApiResponse({
        status: HttpStatus.UNAUTHORIZED,
        description: 'User is not authenticated.',
      }),
      ApiResponse({
        status: HttpStatus.FORBIDDEN,
        description: 'User is not a trainer or does not have permission to update this exercise.',
      }),
    );
  }

  // Remove exercise endpoint docs
  static removeExerciseDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Delete an exercise',
        description:
          'Soft deletes an exercise by setting its status to DELETED. Only accessible to the trainer who created it.',
      }),
      ApiParam({
        name: 'id',
        description: 'Exercise unique identifier (UUID)',
        type: 'string',
        example: '123e4567-e89b-12d3-a456-************',
      }),
      ApiResponse({
        status: HttpStatus.OK,
        description: 'The exercise has been successfully deleted.',
        schema: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              example: '123e4567-e89b-12d3-a456-************',
              description: 'The ID of the deleted exercise',
            },
            status: {
              type: 'string',
              example: ExerciseStatus.DELETED,
              description: 'The new status of the exercise (deleted)',
            },
          },
        },
      }),
      ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        description: 'Invalid ID format.',
      }),
      ApiResponse({
        status: HttpStatus.NOT_FOUND,
        description: 'Exercise not found.',
      }),
      ApiResponse({
        status: HttpStatus.UNAUTHORIZED,
        description: 'User is not authenticated.',
      }),
      ApiResponse({
        status: HttpStatus.FORBIDDEN,
        description: 'User is not a trainer or does not have permission to delete this exercise.',
      }),
    );
  }
}
