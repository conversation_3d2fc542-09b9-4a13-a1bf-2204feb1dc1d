import { EUserRole } from '@app/constants/models/user-role.enum';
import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

@Injectable()
export class TrainerGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const allowedRoles = [EUserRole.TRAINER, EUserRole.USER];

    const hasRole = user.roles.some((role: EUserRole) => allowedRoles.includes(role));

    if (!hasRole) {
      throw new UnauthorizedException('Only trainers can perform this action');
    }

    return true;
  }
}
