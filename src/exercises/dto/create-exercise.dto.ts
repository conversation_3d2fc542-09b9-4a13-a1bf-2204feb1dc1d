import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsUrl, IsNotEmpty } from 'class-validator';
import { DifficultyLevel } from '../enums/difficulty-level.enum';

export class CreateExerciseDto {
  @ApiProperty({
    description: 'Exercise name',
    example: '10 pompek',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Exercise description',
    example: 'Zrób 10 pompek nie badź ...',
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Exercise tags (separated by |)',
    example: 'biceps|triceps|klatka-piersiowa|nogi',
    required: false,
  })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiProperty({
    description: 'Exercise difficulty level',
    enum: DifficultyLevel,
    enumName: 'DifficultyLevel',
    example: DifficultyLevel.MEDIUM,
    required: false,
  })
  @IsOptional()
  @IsEnum(DifficultyLevel)
  difficultyLevel?: DifficultyLevel;

  @ApiProperty({
    description: 'Exercise video URL',
    example: 'https://s3.amazon.com/video.mp4',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  videoUrl?: string;

  @ApiProperty({
    description: 'Exercise image URL',
    example: 'https://image.com/image.jpg',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  imageUrl?: string;

  @ApiProperty({
    description: 'Instructions for performing the exercise',
    example: '1. Stand straight with feet shoulder-width apart. 2. Bend your knees...',
    required: false,
  })
  @IsOptional()
  @IsString()
  instructions?: string;
}
