import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { UserEntity } from '@app/users/entities/user.entity';
import { ApiProperty } from '@nestjs/swagger';
import { ExerciseStatus } from '../enums/exercise-status.enum';
import { DifficultyLevel } from '../enums/difficulty-level.enum';
import { Exclude } from 'class-transformer';

@Entity({ name: 'exercises' })
export class ExerciseEntity {
  @ApiProperty({
    description: 'Unique exercise identifier',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Exercise name',
    example: '10 pompek',
  })
  @Column()
  name: string;

  @ApiProperty({
    description: 'Exercise description',
    example: 'Zrób 10 pompek nie badź ...',
  })
  @Column({ type: 'text' })
  description: string;

  @ApiProperty({
    description: 'Exercise tags',
    example: 'biceps|triceps|klatka-piersiowa|nogi',
  })
  @Column({ nullable: true })
  tags: string;

  @ApiProperty({
    description: 'Exercise difficulty level',
    enum: DifficultyLevel,
    example: DifficultyLevel.MEDIUM,
  })
  @Column({
    type: 'enum',
    enum: DifficultyLevel,
    default: DifficultyLevel.MEDIUM,
  })
  difficultyLevel: DifficultyLevel;

  @ApiProperty({
    description: 'Exercise video URL',
    example: 'https://s3.amazon.com/video.mp4',
    required: false,
  })
  @Column({ nullable: true })
  videoUrl: string;

  @ApiProperty({
    description: 'Exercise image URL',
    example: 'https://image.com/image.jpg',
    required: false,
  })
  @Column({ nullable: true })
  imageUrl: string;

  @ApiProperty({
    description: 'Exercise status',
    enum: ExerciseStatus,
    example: ExerciseStatus.PENDING,
  })
  @Column({
    type: 'enum',
    enum: ExerciseStatus,
    default: ExerciseStatus.PENDING,
  })
  status: ExerciseStatus;

  @ApiProperty({
    description: 'Status message (e.g. rejection reason)',
    example: 'Treść ćwiczenia zawiera nieodpowiednie słownictwo',
    required: false,
  })
  @Column({ nullable: true })
  statusMessage: string;

  @ApiProperty({
    description: 'Exercise creation timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'Exercise last update timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({
    description: 'Creator of the exercise',
    type: () => UserEntity,
  })
  @ManyToOne(() => UserEntity, (user) => 'exercises', { eager: true })
  @Exclude()
  creator: UserEntity;

  @Column()
  creatorId: string;
}
