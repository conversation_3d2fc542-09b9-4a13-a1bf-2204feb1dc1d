import { Injectable, Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';

@Injectable()
export class MailService {
  private readonly logger = new Logger(MailService.name);

  constructor(private readonly mailerService: MailerService) {}

  async sendTestWelcomeEmail(to: string, name: string) {
    try {
      this.logger.log(`Attempting to send welcome email to ${to}`);

      await this.mailerService.sendMail({
        to,
        subject: 'Welcome!',
        template: 'welcome',
        context: {
          name,
        },
      });

      this.logger.log(`Welcome email sent successfully to ${to}`);
    } catch (error) {
      this.logger.error(`Failed to send welcome email to ${to}:`, error);
      throw error; // Re-throw to let controller handle it
    }
  }

  async sendOtc(to: string, code: string) {
    try {
      this.logger.log(`Attempting to send OTC email to ${to}`);

      await this.mailerService.sendMail({
        to,
        subject: 'Your OTC code',
        template: 'otc',
        context: {
          code,
        },
      });

      this.logger.log(`OTC email sent successfully to ${to}`);
    } catch (error) {
      this.logger.error(`Failed to send OTC email to ${to}:`, error);
      throw error; // Re-throw to let controller handle it
    }
  }

  /**
   * Weryfikuje połączenie z serwerem SMTP (SMTP2GO/Gmail).
   * Zwraca szczegółowy komunikat przydatny do diagnostyki.
   */
  async verify(): Promise<{ ok: boolean; message: string }> {
    const transporter = (this.mailerService as any)?.transporter;

    if (!transporter) {
      const message = 'Mailer transporter not initialized (sprawdź konfigurację MailerModule).';
      this.logger.error(message);
      return { ok: false, message };
    }

    try {
      const result = await transporter.verify();
      const message = typeof result === 'string' ? result : 'SMTP connection verified.';
      this.logger.log(message);
      return { ok: true, message };
    } catch (error) {
      const message =
        error instanceof Error
          ? `${error.message}${error['code'] ? ` (code: ${error['code']})` : ''}${
              error['responseCode'] ? `, responseCode: ${error['responseCode']}` : ''
            }`
          : String(error);
      this.logger.error(`SMTP verify failed: ${message}`);
      return { ok: false, message };
    }
  }
}
