import { Body, Controller, Post, HttpCode, HttpStatus, Get, ServiceUnavailableException } from '@nestjs/common';
import { MailService } from '@app/mail/services/mail.service';
import { MAIL_ROUTES } from '@app/constants/routes/mail-routes-names';
import { MailSwaggerDocs } from '@app/mail/docs/mail.docs';
import { SendTestEmailDto } from '@app/mail/dto/send-test-email.dto';
import { SendTestEmailResponseDto } from '@app/mail/dto/send-test-email-response.dto';

@Controller({ version: '1' })
@MailSwaggerDocs.apiTags()
export class MailController {
  constructor(private readonly mailService: MailService) {}

  @Post(MAIL_ROUTES.TEST)
  @HttpCode(HttpStatus.CREATED)
  @MailSwaggerDocs.sendTestEmailDocs()
  async sendTestEmail(@Body() body: SendTestEmailDto): Promise<SendTestEmailResponseDto> {
    const { email, name } = body;
    await this.mailService.sendTestWelcomeEmail(email, name);
    return { message: 'Test email sent successfully' };
  }

  @Get(MAIL_ROUTES.VERIFY)
  @HttpCode(200)
  async verify() {
    const res = await this.mailService.verify();
    if (!res.ok) {
      throw new ServiceUnavailableException({
        status: 'error',
        message: res.message,
      });
    }
    return {
      status: 'ok',
      message: res.message,
    };
  }
}
