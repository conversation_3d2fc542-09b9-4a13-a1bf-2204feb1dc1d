import { ApiProperty } from '@nestjs/swagger';

export class LoginResponseDto {
  @ApiProperty({
    description: 'JWT access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'JWT refresh token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refreshToken: string;

  @ApiProperty({
    description: 'Token expiration information',
    example: {
      accessToken: '2023-01-01T12:00:00Z',
      refreshToken: '2023-01-08T12:00:00Z',
    },
  })
  expiresIn: {
    accessToken: Date;
    refreshToken: Date;
  };

  @ApiProperty({
    description: 'User information',
    example: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      email: '<EMAIL>',
      roles: ['USER'],
    },
  })
  user: {
    id: string;
    email: string;
    roles: string[];
  };
}
