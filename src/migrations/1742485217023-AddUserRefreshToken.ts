import { MigrationInterface, QueryRunner } from "typeorm";

export class AddUserRefreshToken1742485217023 implements MigrationInterface {
    name = 'AddUserRefreshToken1742485217023'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" ADD "refreshToken" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "refreshToken"`);
    }

}
