import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTrainingEntity1744712459400 implements MigrationInterface {
  name = 'AddTrainingEntity1744712459400';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TYPE "public"."trainings_status_enum" AS ENUM('active', 'deleted')
    `);

    await queryRunner.query(`
      CREATE TABLE "trainings" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying NOT NULL,
        "description" text NOT NULL,
        "tags" character varying,
        "status" "public"."trainings_status_enum" NOT NULL DEFAULT 'active',
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "creatorId" uuid NOT NULL,
        CONSTRAINT "PK_trainings_id" PRIMARY KEY ("id")
      )
    `);

    await queryRunner.query(`
      CREATE TABLE "training_exercises" (
        "training_id" uuid NOT NULL,
        "exercise_id" uuid NOT NULL,
        CONSTRAINT "PK_training_exercises" PRIMARY KEY ("training_id", "exercise_id")
      )
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_training_id" ON "training_exercises" ("training_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_exercise_id" ON "training_exercises" ("exercise_id")
    `);

    await queryRunner.query(`
      ALTER TABLE "trainings" 
      ADD CONSTRAINT "FK_trainings_users" 
      FOREIGN KEY ("creatorId") 
      REFERENCES "users"("id") 
      ON DELETE NO ACTION 
      ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "training_exercises" 
      ADD CONSTRAINT "FK_training_exercises_training" 
      FOREIGN KEY ("training_id") 
      REFERENCES "trainings"("id") 
      ON DELETE CASCADE 
      ON UPDATE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "training_exercises" 
      ADD CONSTRAINT "FK_training_exercises_exercise" 
      FOREIGN KEY ("exercise_id") 
      REFERENCES "exercises"("id") 
      ON DELETE CASCADE 
      ON UPDATE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "training_exercises" 
      DROP CONSTRAINT "FK_training_exercises_exercise"
    `);

    await queryRunner.query(`
      ALTER TABLE "training_exercises" 
      DROP CONSTRAINT "FK_training_exercises_training"
    `);

    await queryRunner.query(`
      ALTER TABLE "trainings" 
      DROP CONSTRAINT "FK_trainings_users"
    `);

    await queryRunner.query(`
      DROP INDEX "IDX_exercise_id"
    `);

    await queryRunner.query(`
      DROP INDEX "IDX_training_id"
    `);

    await queryRunner.query(`
      DROP TABLE "training_exercises"
    `);

    await queryRunner.query(`
      DROP TABLE "trainings"
    `);

    await queryRunner.query(`
      DROP TYPE "public"."trainings_status_enum"
    `);
  }
}
