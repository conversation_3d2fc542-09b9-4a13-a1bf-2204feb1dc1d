import { MigrationInterface, QueryRunner } from "typeorm";

export class AddExcercises1744712459399 implements MigrationInterface {
    name = 'AddExcercises1744712459399'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."exercises_difficultylevel_enum" AS ENUM('easy', 'medium', 'hard')`);
        await queryRunner.query(`CREATE TYPE "public"."exercises_status_enum" AS ENUM('pending', 'verified', 'rejected', 'deleted')`);
        await queryRunner.query(`CREATE TABLE "exercises" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "description" text NOT NULL, "tags" character varying, "difficultyLevel" "public"."exercises_difficultylevel_enum" NOT NULL DEFAULT 'medium', "videoUrl" character varying, "imageUrl" character varying, "status" "public"."exercises_status_enum" NOT NULL DEFAULT 'pending', "statusMessage" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "creatorId" uuid NOT NULL, CONSTRAINT "PK_c4c46f5fa89a58ba7c2d894e3c3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "exercises" ADD CONSTRAINT "FK_1fffe0d38ec8748f537af87b276" FOREIGN KEY ("creatorId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "exercises" DROP CONSTRAINT "FK_1fffe0d38ec8748f537af87b276"`);
        await queryRunner.query(`DROP TABLE "exercises"`);
        await queryRunner.query(`DROP TYPE "public"."exercises_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."exercises_difficultylevel_enum"`);
    }

}
