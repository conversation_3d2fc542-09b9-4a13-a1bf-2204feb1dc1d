import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateUserConsentsType1745655090006 implements MigrationInterface {
    name = 'UpdateUserConsentsType1745655090006'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_d40aa6d0eb710116b325ddd42b"`);
        await queryRunner.query(`ALTER TYPE "public"."consent-definitions_code_enum" RENAME TO "consent-definitions_code_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."consent-definitions_code_enum" AS ENUM('GDPR', 'POLICY')`);
        await queryRunner.query(`ALTER TABLE "consent-definitions" ALTER COLUMN "code" TYPE "public"."consent-definitions_code_enum" USING "code"::"text"::"public"."consent-definitions_code_enum"`);
        await queryRunner.query(`DROP TYPE "public"."consent-definitions_code_enum_old"`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_d40aa6d0eb710116b325ddd42b" ON "consent-definitions" ("code", "version") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_d40aa6d0eb710116b325ddd42b"`);
        await queryRunner.query(`CREATE TYPE "public"."consent-definitions_code_enum_old" AS ENUM('GDPR', 'PRIVACY_POLICY', 'MARKETING')`);
        await queryRunner.query(`ALTER TABLE "consent-definitions" ALTER COLUMN "code" TYPE "public"."consent-definitions_code_enum_old" USING "code"::"text"::"public"."consent-definitions_code_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."consent-definitions_code_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."consent-definitions_code_enum_old" RENAME TO "consent-definitions_code_enum"`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_d40aa6d0eb710116b325ddd42b" ON "consent-definitions" ("code", "version") `);
    }

}
