import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { GroupsService } from '../services/groups.service';
import { CreateGroupRequestDto, CreateGroupResponseDto } from '../dto/create-group.dto';
import { UpdateGroupRequestDto, UpdateGroupResponseDto } from '../dto/update-group.dto';
import { JwtAuthGuard } from '@app/tools/guards/jwt-auth.guard';
import { GROUP_ROUTES } from '@app/constants/routes/group-routes-names';
import { UserEntity } from '@app/users/entities/user.entity';
import { GroupsSwaggerDocs } from '../docs/groups.docs';
// import { TrainerGuard } from '@app/groups/guards/trainer.guard';
import { CurrentBaseUser } from '@app/tools/decorators/current-user.decorator';
import { GetGroupResponseDto } from '../dto/get-group.dto';

@GroupsSwaggerDocs.apiTags()
@Controller({ version: '1' })
@UseGuards(JwtAuthGuard /*TrainerGuard -> temp commented due to missing user creation with trainer role*/)
@GroupsSwaggerDocs.apiBearerAuth()
export class GroupsController {
  constructor(private readonly groupsService: GroupsService) {}

  @Post(GROUP_ROUTES.GROUP)
  @GroupsSwaggerDocs.createGroupDocs()
  async createGroup(
    @Body() CreateGroupRequestDto: CreateGroupRequestDto,
    @CurrentBaseUser() owner: UserEntity,
  ): Promise<CreateGroupResponseDto> {
    return this.groupsService.createGroup(CreateGroupRequestDto, owner);
  }

  @Get(GROUP_ROUTES.GROUP)
  @GroupsSwaggerDocs.getGroupsDocs()
  async getGroups(@CurrentBaseUser() owner: UserEntity): Promise<GetGroupResponseDto[]> {
    return this.groupsService.getGroups(owner);
  }

  @Get(GROUP_ROUTES.GROUP_BY_ID)
  @GroupsSwaggerDocs.getGroupByIdDocs()
  async getGroupById(
    @Param('groupId') groupId: string,
    @CurrentBaseUser() owner: UserEntity,
  ): Promise<GetGroupResponseDto> {
    return this.groupsService.getGroupById(groupId, owner);
  }

  @Delete(GROUP_ROUTES.GROUP_BY_ID)
  @GroupsSwaggerDocs.deleteGroupDocs()
  async deleteGroup(
    @Param('groupId') groupId: string,
    @CurrentBaseUser() owner: UserEntity,
  ): Promise<{ message: string }> {
    return this.groupsService.deleteGroup(groupId, owner);
  }

  @Put(GROUP_ROUTES.GROUP_BY_ID)
  @GroupsSwaggerDocs.updateGroupDocs()
  async updateGroup(
    @Param('groupId') groupId: string,
    @Body() UpdateGroupRequestDto: UpdateGroupRequestDto,
    @CurrentBaseUser() owner: UserEntity,
  ): Promise<UpdateGroupResponseDto> {
    return this.groupsService.updateGroup(groupId, UpdateGroupRequestDto, owner);
  }

  @Post(GROUP_ROUTES.JOIN_GROUP)
  @GroupsSwaggerDocs.joinGroupDocs()
  @HttpCode(HttpStatus.OK)
  async joinGroup(
    @Query('invitationToken') invitationToken: string,
    @CurrentBaseUser() user: UserEntity,
  ): Promise<{ message: string }> {
    return this.groupsService.joinGroup(invitationToken, user);
  }

  @Post(GROUP_ROUTES.ADD_USER_TO_GROUP)
  @GroupsSwaggerDocs.addUserToGroupDocs()
  @HttpCode(HttpStatus.OK)
  async addUserToGroup(
    @Param('groupId') groupId: string,
    @Param('userId') memberId: string,
    @CurrentBaseUser() owner: UserEntity,
  ): Promise<{ message: string }> {
    return this.groupsService.addUserToGroup(groupId, memberId, owner);
  }

  @Delete(GROUP_ROUTES.REMOVE_USER_FROM_GROUP)
  @GroupsSwaggerDocs.removeUserFromGroupDocs()
  async removeUserFromGroup(
    @Param('groupId') groupId: string,
    @Param('userId') userId: string,
    // @CurrentBaseUser() owner: UserEntity, // I think we should comment out this because member can leave group by himself
  ): Promise<{ message: string }> {
    return this.groupsService.removeUserFromGroup(groupId, userId);
  }
}
