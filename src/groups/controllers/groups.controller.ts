import { Body, Controller, Delete, Get, Param, Post, Put, UseGuards } from '@nestjs/common';
import { GroupsService } from '../services/groups.service';
import { CreateGroupDto } from '../dto/create-group.dto';
import { UpdateGroupDto } from '../dto/update-group.dto';
import { JwtAuthGuard } from '@app/tools/guards/jwt-auth.guard';
import { GROUP_ROUTES } from '@app/constants/routes/group-routes-names';
import { UserEntity } from '@app/users/entities/user.entity';
import { GroupsSwaggerDocs } from '../docs/groups.docs';
import { TrainerGuard } from '@app/groups/guards/trainer.guard';
import { GroupEntity } from '../entities/group.entity';
import { CurrentBaseUser } from '@app/tools/decorators/current-user.decorator';
import { GroupUserEntity } from '../entities/group-user.entity';

@GroupsSwaggerDocs.apiTags()
@Controller({ version: '1' })
@UseGuards(JwtAuthGuard, TrainerGuard)
@GroupsSwaggerDocs.apiBearerAuth()
export class GroupsController {
  constructor(private readonly groupsService: GroupsService) {}

  @Post(GROUP_ROUTES.GROUP)
  @GroupsSwaggerDocs.createGroupDocs()
  async createGroup(@Body() createGroupDto: CreateGroupDto, @CurrentBaseUser() user: UserEntity): Promise<GroupEntity> {
    return this.groupsService.createGroup(createGroupDto, user);
  }

  @Get(GROUP_ROUTES.GROUP)
  @GroupsSwaggerDocs.getGroupsDocs()
  async getGroups(@CurrentBaseUser() user: UserEntity): Promise<GroupUserEntity[]> {
    return this.groupsService.getGroups(user);
  }

  @Get(GROUP_ROUTES.GROUP_BY_ID)
  @GroupsSwaggerDocs.getGroupByIdDocs()
  async getGroupById(@Param('groupId') groupId: string, @CurrentBaseUser() user: UserEntity): Promise<GroupEntity> {
    return this.groupsService.getGroupById(groupId, user);
  }

  @Put(GROUP_ROUTES.GROUP_BY_ID)
  @GroupsSwaggerDocs.updateGroupDocs()
  async updateGroup(
    @Param('groupId') groupId: string,
    @Body() updateGroupDto: UpdateGroupDto,
    @CurrentBaseUser() user: UserEntity,
  ): Promise<GroupEntity> {
    return this.groupsService.updateGroup(groupId, updateGroupDto, user);
  }

  @Post(GROUP_ROUTES.ADD_USER_TO_GROUP)
  @GroupsSwaggerDocs.addUserToGroupDocs()
  async addUserToGroup(
    @Param('groupId') groupId: string,
    @Param('userId') userId: string,
    @CurrentBaseUser() user: UserEntity,
  ): Promise<void> {
    return this.groupsService.addUserToGroup(groupId, userId, user);
  }

  @Delete(GROUP_ROUTES.REMOVE_USER_FROM_GROUP)
  @GroupsSwaggerDocs.removeUserFromGroupDocs()
  async removeUserFromGroup(
    @Param('groupId') groupId: string,
    @Param('userId') userId: string,
    @CurrentBaseUser() user: UserEntity,
  ): Promise<void> {
    return this.groupsService.removeUserFromGroup(groupId, userId, user);
  }
}
