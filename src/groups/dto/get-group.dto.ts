import { UserEntity } from '@app/users/entities/user.entity';
import { CreateGroupResponseDto } from './create-group.dto';
import { GroupEntity } from '../entities/group.entity';
import { ApiProperty } from '@nestjs/swagger';
import { TrainingEntity } from '@app/training/entities/training.entity';

export class GroupTrainingPlanDto {
  @ApiProperty({
    description: 'Unique training plan group relationship identifier',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Training plan ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  trainingPlanId: string;

  @ApiProperty({
    description: 'Training plan name',
    example: 'Plan na tydzień',
  })
  trainingPlanName: string;

  @ApiProperty({
    description: 'Training plan description',
    example: 'Super plan treningowy',
  })
  trainingPlanDescription?: string;

  @ApiProperty({
    description: 'Start date of the training plan for the group',
    example: '2025-05-06',
  })
  startDate: string;

  @ApiProperty({
    description: 'Relationship creation timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Trainings included in the training plan',
    type: () => [TrainingEntity],
  })
  trainings: TrainingEntity[];
}

export class GetGroupResponseDto extends CreateGroupResponseDto {
  @ApiProperty({
    description: 'Training plans assigned to this group with full details',
    type: () => [GroupTrainingPlanDto],
  })
  assignedTrainingPlans: GroupTrainingPlanDto[];
}

export class GetGroupUserResponseDto {
  @ApiProperty({
    description: 'Unique group-user relationship identifier',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  group: GroupEntity;

  user: UserEntity;

  @ApiProperty({
    description: 'Relationship creation timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  createdAt: Date;
}
