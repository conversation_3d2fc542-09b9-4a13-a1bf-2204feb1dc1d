import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';

import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { BadRequestException, ForbiddenException, NotFoundException } from '@nestjs/common';
import { GroupsService } from './groups.service';
import { GroupEntity } from '../entities/group.entity';
import { GroupUserEntity } from '../entities/group-user.entity';
import { CreateGroupRequestDto } from '../dto/create-group.dto';
import { UpdateGroupRequestDto } from '../dto/update-group.dto';
import { UserEntity } from '@app/users/entities/user.entity';

describe('GroupsService', () => {
  let service: GroupsService;

  const mockGroupRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
  };

  const mockGroupUserRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
  };

  const mockConfigService = {
    getOrThrow: jest.fn(),
  };

  const mockUser = {
    id: 'user-id',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'hashedPassword',
    consents: ['RODO'],
    roles: ['trainer'],
    createdAt: new Date(),
  } as unknown as UserEntity;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GroupsService,
        {
          provide: getRepositoryToken(GroupEntity),
          useValue: mockGroupRepository,
        },
        {
          provide: getRepositoryToken(GroupUserEntity),
          useValue: mockGroupUserRepository,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<GroupsService>(GroupsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createGroup', () => {
    it('should create a new group and add the owner as a member', async () => {
      const CreateGroupRequestDto: CreateGroupRequestDto = {
        name: 'Test Group',
        description: 'Test Description',
        tags: 'tag1|tag2',
      };

      const mockGroup = {
        id: 'group-id',
        name: CreateGroupRequestDto.name,
        description: CreateGroupRequestDto.description,
        tags: CreateGroupRequestDto.tags,
        ownerId: mockUser.id,
        invitationUrl: 'placeholder',
      };

      mockGroupRepository.create.mockReturnValue(mockGroup);
      mockGroupRepository.save.mockResolvedValueOnce(mockGroup).mockResolvedValueOnce(mockGroup);
      mockJwtService.sign.mockReturnValue('jwt-token');
      mockConfigService.getOrThrow.mockReturnValueOnce('secret').mockReturnValueOnce('https://invite-to-group.pl');

      const result = await service.createGroup(CreateGroupRequestDto, mockUser);

      expect(mockGroupRepository.create).toHaveBeenCalledWith({
        name: CreateGroupRequestDto.name,
        description: CreateGroupRequestDto.description,
        tags: CreateGroupRequestDto.tags,
        ownerId: mockUser.id,
        invitationUrl: 'placeholder',
      });
      expect(mockGroupRepository.save).toHaveBeenCalledTimes(2);
      expect(mockGroupUserRepository.save).toHaveBeenCalledWith({
        group: mockGroup,
        user: mockUser,
      });
      expect(result).toEqual({
        id: mockGroup.id,
        name: mockGroup.name,
        description: mockGroup.description,
        tags: mockGroup.tags,
        invitationUrl: mockGroup.invitationUrl,
        ownerId: mockUser.id,
      });
    });
  });

  describe('getGroups', () => {
    it('should return all groups for a user', async () => {
      const mockGroupUserEntities = [
        {
          id: 'group-user-id-1',
          group: {
            id: 'group-id-1',
            name: 'Group 1',
            description: 'Description 1',
            tags: 'tag1|tag2',
            invitationUrl: 'https://invite-to-group.pl/token1',
          },
          user: mockUser,
          createdAt: new Date(),
        },
        {
          id: 'group-user-id-2',
          group: {
            id: 'group-id-2',
            name: 'Group 2',
            description: 'Description 2',
            tags: 'tag3|tag4',
            invitationUrl: 'https://invite-to-group.pl/token2',
          },
          user: mockUser,
          createdAt: new Date(),
        },
      ];

      const expectedGroups = mockGroupUserEntities.map((groupUser) => groupUser.group);

      mockGroupUserRepository.find.mockResolvedValue(mockGroupUserEntities);

      const result = await service.getGroups(mockUser);

      expect(mockGroupUserRepository.find).toHaveBeenCalledWith({
        where: { user: { id: mockUser.id } },
        relations: ['group'],
      });
      expect(result).toEqual(expectedGroups);
    });

    it('should return an empty array if user has no groups', async () => {
      mockGroupUserRepository.find.mockResolvedValue([]);

      const result = await service.getGroups(mockUser);

      expect(result).toEqual([]);
    });
  });

  describe('getGroupById', () => {
    it('should return a group by id if user is a member', async () => {
      const groupId = 'group-id';
      const mockGroupUser = {
        group: {
          id: groupId,
          name: 'Test Group',
          description: 'Test Description',
          tags: 'tag1|tag2',
          invitationUrl: 'https://invite-to-group.pl/token',
        },
      };

      const mockGroup = {
        id: groupId,
        name: 'Test Group',
        description: 'Test Description',
        tags: 'tag1|tag2',
        invitationUrl: 'https://invite-to-group.pl/token',
      };

      mockGroupUserRepository.findOne.mockResolvedValue(mockGroupUser);
      mockGroupRepository.findOne.mockResolvedValue(mockGroup);

      const result = await service.getGroupById(groupId, mockUser);

      expect(mockGroupUserRepository.findOne).toHaveBeenCalledWith({
        where: { group: { id: groupId }, user: { id: mockUser.id } },
        relations: ['group'],
      });
      expect(mockGroupRepository.findOne).toHaveBeenCalledWith({
        where: { id: groupId },
      });
      expect(result).toEqual(mockGroup);
    });

    it('should throw ForbiddenException if user is not a member of the group', async () => {
      const groupId = 'group-id';
      mockGroupUserRepository.findOne.mockResolvedValue(null);

      await expect(service.getGroupById(groupId, mockUser)).rejects.toThrow(ForbiddenException);
      expect(mockGroupUserRepository.findOne).toHaveBeenCalledWith({
        where: { group: { id: groupId }, user: { id: mockUser.id } },
        relations: ['group'],
      });
    });

    it('should throw NotFoundException if group does not exist', async () => {
      const groupId = 'non-existent-group-id';
      mockGroupUserRepository.findOne.mockResolvedValue({ group: { id: groupId } });
      mockGroupRepository.findOne.mockResolvedValue(null);

      await expect(service.getGroupById(groupId, mockUser)).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateGroup', () => {
    it('should update a group if user is the owner', async () => {
      const groupId = 'group-id';
      const UpdateGroupRequestDto: UpdateGroupRequestDto = {
        name: 'Updated Group Name',
        description: 'Updated Description',
        tags: 'updated|tags',
      };

      const mockGroup = {
        id: groupId,
        name: 'Original Group Name',
        description: 'Original Description',
        tags: 'original|tags',
        ownerId: mockUser.id,
        invitationUrl: 'https://invite-to-group.pl/token',
      };

      const updatedGroup = {
        ...mockGroup,
        name: UpdateGroupRequestDto.name,
        description: UpdateGroupRequestDto.description,
        tags: UpdateGroupRequestDto.tags,
      };

      mockGroupRepository.findOne.mockResolvedValue(mockGroup);
      mockGroupRepository.save.mockResolvedValue(updatedGroup);

      const result = await service.updateGroup(groupId, UpdateGroupRequestDto, mockUser);

      expect(mockGroupRepository.findOne).toHaveBeenCalledWith({
        where: { id: groupId },
      });
      expect(mockGroupRepository.save).toHaveBeenCalledWith({
        ...mockGroup,
        name: UpdateGroupRequestDto.name,
        description: UpdateGroupRequestDto.description,
        tags: UpdateGroupRequestDto.tags,
      });
      expect(result).toEqual({
        id: updatedGroup.id,
        name: updatedGroup.name,
        description: updatedGroup.description,
        tags: updatedGroup.tags,
        invitationUrl: updatedGroup.invitationUrl,
        ownerId: mockUser.id,
      });
    });

    it('should throw NotFoundException if group does not exist', async () => {
      const groupId = 'non-existent-group-id';
      const UpdateGroupRequestDto: UpdateGroupRequestDto = {
        name: 'Updated Group Name',
      };

      mockGroupRepository.findOne.mockResolvedValue(null);

      await expect(service.updateGroup(groupId, UpdateGroupRequestDto, mockUser)).rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException if user is not the owner', async () => {
      const groupId = 'group-id';
      const UpdateGroupRequestDto: UpdateGroupRequestDto = {
        name: 'Updated Group Name',
      };

      const mockGroup = {
        id: groupId,
        name: 'Original Group Name',
        ownerId: 'different-user-id', // Different from mockUser.id
      };

      mockGroupRepository.findOne.mockResolvedValue(mockGroup);

      await expect(service.updateGroup(groupId, UpdateGroupRequestDto, mockUser)).rejects.toThrow(ForbiddenException);
    });

    it('should update only the provided fields', async () => {
      const groupId = 'group-id';
      const UpdateGroupRequestDto: UpdateGroupRequestDto = {
        name: 'Updated Group Name',
        // description and tags are not provided
      };

      const mockGroup = {
        id: groupId,
        name: 'Original Group Name',
        description: 'Original Description',
        tags: 'original|tags',
        ownerId: mockUser.id,
        invitationUrl: 'https://invite-to-group.pl/token',
      };

      const updatedGroup = {
        ...mockGroup,
        name: UpdateGroupRequestDto.name,
        // description and tags remain unchanged
      };

      mockGroupRepository.findOne.mockResolvedValue(mockGroup);
      mockGroupRepository.save.mockResolvedValue(updatedGroup);

      const result = await service.updateGroup(groupId, UpdateGroupRequestDto, mockUser);

      expect(mockGroupRepository.save).toHaveBeenCalledWith({
        ...mockGroup,
        name: UpdateGroupRequestDto.name,
        // description and tags should remain unchanged
      });
      expect(result.name).toEqual(UpdateGroupRequestDto.name);
      expect(result.description).toEqual(mockGroup.description);
      expect(result.tags).toEqual(mockGroup.tags);
    });
  });

  describe('addUserToGroup', () => {
    it('should add a user to a group', async () => {
      const groupId = 'group-id';
      const userId = 'user-to-add-id';

      const mockGroup = {
        id: groupId,
        name: 'Test Group',
      };

      mockGroupRepository.findOne.mockResolvedValue(mockGroup);
      mockGroupUserRepository.findOne.mockResolvedValue(null); // User is not already in the group
      mockGroupUserRepository.save.mockResolvedValue({});

      await service.addUserToGroup(groupId, userId, mockUser);

      expect(mockGroupRepository.findOne).toHaveBeenCalledWith({
        where: { id: groupId },
      });
      expect(mockGroupUserRepository.findOne).toHaveBeenCalledWith({
        where: { group: { id: groupId }, user: { id: userId } },
      });
      expect(mockGroupUserRepository.save).toHaveBeenCalledWith({
        group: { id: groupId },
        user: { id: userId },
      });
    });

    it('should throw NotFoundException if group does not exist', async () => {
      const groupId = 'non-existent-group-id';
      const userId = 'user-id';

      mockGroupRepository.findOne.mockResolvedValue(null);

      await expect(service.addUserToGroup(groupId, userId, mockUser)).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if user is already a member', async () => {
      const groupId = 'group-id';
      const userId = 'user-id';

      const mockGroup = {
        id: groupId,
        name: 'Test Group',
      };

      mockGroupRepository.findOne.mockResolvedValue(mockGroup);
      mockGroupUserRepository.findOne.mockResolvedValue({}); // User is already in the group

      await expect(service.addUserToGroup(groupId, userId, mockUser)).rejects.toThrow(BadRequestException);
    });
  });

  describe('removeUserFromGroup', () => {
    it('should remove a user from a group if current user is the owner', async () => {
      const groupId = 'group-id';
      const userId = 'user-to-remove-id';

      const mockGroup = {
        id: groupId,
        name: 'Test Group',
        ownerId: mockUser.id,
      };

      const mockGroupUser = {
        id: 'group-user-id',
        group: mockGroup,
        user: { id: userId },
      };

      mockGroupRepository.findOne.mockResolvedValue(mockGroup);
      mockGroupUserRepository.findOne.mockResolvedValue(mockGroupUser);
      mockGroupUserRepository.remove.mockResolvedValue({});

      await service.removeUserFromGroup(groupId, userId);

      expect(mockGroupRepository.findOne).toHaveBeenCalledWith({
        where: { id: groupId },
      });
      expect(mockGroupUserRepository.findOne).toHaveBeenCalledWith({
        where: { group: { id: groupId }, user: { id: userId } },
      });
      expect(mockGroupUserRepository.remove).toHaveBeenCalledWith(mockGroupUser);
    });

    it('should remove a user from a group if current user is removing themselves', async () => {
      const groupId = 'group-id';
      const userId = mockUser.id; // Same as current user

      const mockGroup = {
        id: groupId,
        name: 'Test Group',
        ownerId: 'different-owner-id', // Not the current user
      };

      const mockGroupUser = {
        id: 'group-user-id',
        group: mockGroup,
        user: { id: userId },
      };

      mockGroupRepository.findOne.mockResolvedValue(mockGroup);
      mockGroupUserRepository.findOne.mockResolvedValue(mockGroupUser);
      mockGroupUserRepository.remove.mockResolvedValue({});

      await service.removeUserFromGroup(groupId, userId);

      expect(mockGroupUserRepository.remove).toHaveBeenCalledWith(mockGroupUser);
    });

    it('should throw NotFoundException if group does not exist', async () => {
      const groupId = 'non-existent-group-id';
      const userId = 'user-id';

      mockGroupRepository.findOne.mockResolvedValue(null);

      await expect(service.removeUserFromGroup(groupId, userId)).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException if user is not a member of the group', async () => {
      const groupId = 'group-id';
      const userId = 'user-id';

      const mockGroup = {
        id: groupId,
        name: 'Test Group',
      };

      mockGroupRepository.findOne.mockResolvedValue(mockGroup);
      mockGroupUserRepository.findOne.mockResolvedValue(null); // User is not in the group

      await expect(service.removeUserFromGroup(groupId, userId)).rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException if current user is not the owner and not removing themselves', async () => {
      const groupId = 'group-id';
      const userId = 'different-user-id'; // Different from current user

      const mockGroup = {
        id: groupId,
        name: 'Test Group',
        ownerId: 'different-owner-id', // Not the current user
      };

      const mockGroupUser = {
        id: 'group-user-id',
        group: mockGroup,
        user: { id: userId },
      };

      mockGroupRepository.findOne.mockResolvedValue(mockGroup);
      mockGroupUserRepository.findOne.mockResolvedValue(mockGroupUser);

      await expect(service.removeUserFromGroup(groupId, userId)).rejects.toThrow(ForbiddenException);
    });
  });

  describe('generateInvitationUrl', () => {
    it('should generate a valid invitation URL', () => {
      const groupId = 'group-id';
      const token = 'jwt-token';
      const baseUrl = 'https://invite-to-group.pl';
      const secret = 'secret-key';

      mockJwtService.sign.mockReturnValue(token);
      mockConfigService.getOrThrow.mockReturnValueOnce(secret).mockReturnValueOnce(baseUrl);

      // We need to access the private method, so we use any to bypass TypeScript's type checking
      const result = (service as any).generateInvitationUrl(groupId);

      expect(mockJwtService.sign).toHaveBeenCalledWith(
        { groupId },
        {
          secret,
          expiresIn: '30d',
        },
      );
      expect(mockConfigService.getOrThrow).toHaveBeenCalledWith('GROUP_INVITATION_SECRET');
      expect(mockConfigService.getOrThrow).toHaveBeenCalledWith('GROUP_INVITATION_BASE_URL');
      expect(result).toEqual(`${baseUrl}?token=${token}`);
    });
  });

  // Metoda decodeInvitationToken została usunięta lub zmieniona w implementacji
});
