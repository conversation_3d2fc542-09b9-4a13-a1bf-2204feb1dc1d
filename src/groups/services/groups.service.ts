import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { GroupEntity } from '../entities/group.entity';
import { GroupUserEntity } from '../entities/group-user.entity';
import { CreateGroupDto } from '../dto/create-group.dto';
import { UpdateGroupDto } from '../dto/update-group.dto';
import { UserEntity } from '@app/users/entities/user.entity';

@Injectable()
export class GroupsService {
  constructor(
    @InjectRepository(GroupEntity)
    private readonly groupRepository: Repository<GroupEntity>,
    @InjectRepository(GroupUserEntity)
    private readonly groupUserRepository: Repository<GroupUserEntity>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Generate a secure invitation URL for a group
   * @param groupId Group ID to encode in the URL
   * @returns Invitation URL with encoded group ID
   */
  private generateInvitationUrl(groupId: string): string {
    const token = this.jwtService.sign(
      { groupId },
      {
        secret: this.configService.getOrThrow('GROUP_INVITATION_SECRET'),
        expiresIn: '30d', // Invitation valid for 30 days
      },
    );
    return `${this.configService.getOrThrow('GROUP_INVITATION_BASE_URL')}?token=${token}`;
  }

  /**
   * Create a new group
   * @param createGroupDto Group creation data
   * @param user Current user (owner)
   * @returns Created group
   */
  async createGroup(createGroupDto: CreateGroupDto, user: UserEntity): Promise<GroupEntity> {
    const { name, description, tags } = createGroupDto;

    // Create the group
    const group = this.groupRepository.create({
      name,
      description,
      tags,
      ownerId: user.id,
      invitationUrl: 'placeholder', // Will be updated after saving
    });

    const savedGroup = await this.groupRepository.save(group);

    // Generate and update the invitation URL
    savedGroup.invitationUrl = this.generateInvitationUrl(savedGroup.id);
    await this.groupRepository.save(savedGroup);

    // Add the owner as a member of the group
    await this.groupUserRepository.save({
      group: savedGroup,
      user,
    });

    return savedGroup;
  }

  /**
   * Get all groups for a user
   * @param user Current user
   * @returns List of groups the user belongs to
   */
  async getGroups(user: UserEntity): Promise<GroupUserEntity[]> {
    // Find all group-user relationships for the user
    return await this.groupUserRepository.find({
      where: { user: { id: user.id } },
      relations: ['group'],
    });
  }

  /**
   * Get group details by ID
   * @param groupId Group ID
   * @param user Current user
   * @returns Group details including users
   */
  async getGroupById(groupId: string, user: UserEntity): Promise<GroupEntity> {
    // Check if the user is a member of the group
    const groupUser = await this.groupUserRepository.findOne({
      where: { group: { id: groupId }, user: { id: user.id } },
      relations: ['group'],
    });

    if (!groupUser) {
      throw new ForbiddenException('You are not a member of this group');
    }

    // Get the group with all its users
    const group = await this.groupRepository.findOne({
      where: { id: groupId },
    });

    if (!group) {
      throw new NotFoundException('Group not found');
    }

    return group;
  }

  /**
   * Update group details
   * @param groupId Group ID
   * @param updateGroupDto Group update data
   * @param user Current user
   * @returns Updated group
   */
  async updateGroup(groupId: string, updateGroupDto: UpdateGroupDto, user: UserEntity): Promise<GroupEntity> {
    // Get the group
    const group = await this.groupRepository.findOne({
      where: { id: groupId },
    });

    if (!group) {
      throw new NotFoundException('Group not found');
    }

    // Check if the user is the owner of the group
    if (group.ownerId !== user.id) {
      throw new ForbiddenException('Only the group owner can update the group');
    }

    // Update only the provided fields
    if (updateGroupDto.name) group.name = updateGroupDto.name;
    if (updateGroupDto.description !== undefined) group.description = updateGroupDto.description;
    if (updateGroupDto.tags !== undefined) group.tags = updateGroupDto.tags;

    return await this.groupRepository.save(group);
  }

  /**
   * Add a user to a group
   * @param groupId Group ID
   * @param userId User ID to add
   * @param currentUser Current user making the request
   */
  async addUserToGroup(groupId: string, userId: string, currentUser: UserEntity): Promise<void> {
    // Check if the group exists
    const group = await this.groupRepository.findOne({
      where: { id: groupId },
    });

    if (!group) {
      throw new NotFoundException('Group not found');
    }

    // Check if the user is already in the group
    const existingGroupUser = await this.groupUserRepository.findOne({
      where: { group: { id: groupId }, user: { id: userId } },
    });

    if (existingGroupUser) {
      throw new BadRequestException('User is already a member of this group');
    }

    // Add the user to the group
    await this.groupUserRepository.save({
      group: { id: groupId },
      user: { id: userId },
    });
  }

  /**
   * Remove a user from a group
   * @param groupId Group ID
   * @param userId User ID to remove
   * @param currentUser Current user making the request
   */
  async removeUserFromGroup(groupId: string, userId: string, currentUser: UserEntity): Promise<void> {
    // Check if the group exists
    const group = await this.groupRepository.findOne({
      where: { id: groupId },
    });

    if (!group) {
      throw new NotFoundException('Group not found');
    }

    // Check if the user is in the group
    const groupUser = await this.groupUserRepository.findOne({
      where: { group: { id: groupId }, user: { id: userId } },
    });

    if (!groupUser) {
      throw new NotFoundException('User is not a member of this group');
    }

    // Check if the current user is the owner or the user themselves
    if (group.ownerId !== currentUser.id && userId !== currentUser.id) {
      throw new ForbiddenException('Only the group owner or the user themselves can remove a user from the group');
    }

    // Remove the user from the group
    await this.groupUserRepository.remove(groupUser);
  }
}
