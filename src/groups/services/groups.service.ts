import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { GroupEntity } from '../entities/group.entity';
import { GroupUserEntity } from '../entities/group-user.entity';
import { CreateGroupRequestDto } from '../dto/create-group.dto';
import { UpdateGroupRequestDto } from '../dto/update-group.dto';
import { UserEntity } from '@app/users/entities/user.entity';

export type GroupInvitationTokenPayload = {
  groupId: string;
};

@Injectable()
export class GroupsService {
  private readonly logger = new Logger(GroupsService.name);

  constructor(
    @InjectRepository(GroupEntity)
    private readonly groupRepository: Repository<GroupEntity>,
    @InjectRepository(GroupUserEntity)
    private readonly groupUserRepository: Repository<GroupUserEntity>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  async createGroup(data: CreateGroupRequestDto, owner: UserEntity): Promise<GroupEntity> {
    const group = this.groupRepository.create({
      ...data,
      ownerId: owner.id,
      invitationUrl: 'placeholder',
    });

    const savedGroup = await this.groupRepository.save(group);
    savedGroup.invitationUrl = this.generateInvitationUrl(savedGroup.id);
    await this.groupRepository.save(savedGroup);

    await this.groupUserRepository.save({
      group: savedGroup,
      user: owner,
    });

    return savedGroup;
  }

  async deleteGroup(groupId: string, owner: UserEntity): Promise<{ message: string }> {
    const group = await this.getGroupOrThrow(groupId);
    this.assertOwnership(group, owner);

    await this.groupRepository.remove(group);
    return { message: 'Group successfully deleted' };
  }

  async getGroups(owner: UserEntity): Promise<any[]> {
    try {
      const groupUsers = await this.groupUserRepository.find({
        where: { user: { id: owner.id } },
        relations: [
          'group',
          'group.trainingPlanGroups',
          'group.trainingPlanGroups.trainingPlan',
          'group.trainingPlanGroups.trainingPlan.trainingPlanTrainings',
          'group.trainingPlanGroups.trainingPlan.trainingPlanTrainings.training',
        ],
      });

      this.logger.debug(`Found ${groupUsers.length} groups for user ${owner.id}`);

      return groupUsers.map((groupUser) => {
        const group = groupUser.group;

        // Transform the data to include full training plan details
        const assignedTrainingPlans = (group.trainingPlanGroups || [])
          .filter((tpg) => tpg && tpg.trainingPlan) // Filter out invalid records
          .map((tpg) => ({
            id: tpg.id,
            trainingPlanId: tpg.trainingPlanId,
            trainingPlanName: tpg.trainingPlan?.name || 'Unknown Plan',
            trainingPlanDescription: tpg.trainingPlan?.description || null,
            startDate: tpg.startDate ? tpg.startDate.toISOString().split('T')[0] : null, // Format as YYYY-MM-DD
            createdAt: tpg.createdAt,
            trainings: tpg.trainingPlan?.trainingPlanTrainings
              ? tpg.trainingPlan.trainingPlanTrainings
                  .sort((a, b) => a.orderIndex - b.orderIndex)
                  .map((tpt) => tpt.training)
                  .filter((training) => training) // Filter out null trainings
              : [],
          }));

        return {
          ...group,
          assignedTrainingPlans,
          // Remove the original trainingPlanGroups to avoid confusion
          trainingPlanGroups: undefined,
        };
      });
    } catch (error) {
      this.logger.error(`Error fetching groups for user ${owner.id}:`, error);
      throw error;
    }
  }

  async getGroupById(groupId: string, owner: UserEntity): Promise<any> {
    const group = await this.groupRepository.findOne({
      where: { id: groupId },
      relations: [
        'groupUsers.user',
        'trainingPlanGroups',
        'trainingPlanGroups.trainingPlan',
        'trainingPlanGroups.trainingPlan.trainingPlanTrainings',
        'trainingPlanGroups.trainingPlan.trainingPlanTrainings.training',
      ],
    });

    if (!group) {
      throw new NotFoundException(`Group with id ${groupId} not found`);
    }

    await this.assertMembership(groupId, owner.id);

    // Transform the data to include full training plan details
    const assignedTrainingPlans = (group.trainingPlanGroups || [])
      .filter((tpg) => tpg && tpg.trainingPlan) // Filter out invalid records
      .map((tpg) => ({
        id: tpg.id,
        trainingPlanId: tpg.trainingPlanId,
        trainingPlanName: tpg.trainingPlan?.name || 'Unknown Plan',
        trainingPlanDescription: tpg.trainingPlan?.description || null,
        startDate: tpg.startDate ? tpg.startDate.toISOString().split('T')[0] : null, // Format as YYYY-MM-DD
        createdAt: tpg.createdAt,
        trainings: tpg.trainingPlan?.trainingPlanTrainings
          ? tpg.trainingPlan.trainingPlanTrainings
              .sort((a, b) => a.orderIndex - b.orderIndex)
              .map((tpt) => tpt.training)
              .filter((training) => training) // Filter out null trainings
          : [],
      }));

    return {
      ...group,
      assignedTrainingPlans,
      // Remove the original trainingPlanGroups to avoid confusion
      trainingPlanGroups: undefined,
    };
  }

  async updateGroup(groupId: string, data: UpdateGroupRequestDto, owner: UserEntity): Promise<GroupEntity> {
    const group = await this.getGroupOrThrow(groupId);
    this.assertOwnership(group, owner);

    Object.assign(group, data);

    return await this.groupRepository.save(group);
  }

  async addUserToGroup(groupId: string, memberId: string, owner: UserEntity): Promise<{ message: string }> {
    const group = await this.getGroupOrThrow(groupId);
    this.assertOwnership(group, owner);

    const exists = await this.groupUserRepository.findOne({
      where: { group: { id: groupId }, user: { id: memberId } },
    });

    if (exists) {
      throw new BadRequestException('User is already a member of this group');
    }

    await this.groupUserRepository.save({
      group: { id: groupId },
      user: { id: memberId },
    });
    return { message: 'User successfully added to group' };
  }

  async joinGroup(invitationToken: string, user: UserEntity): Promise<{ message: string }> {
    const { groupId } = this.verifyInvitationToken(invitationToken);

    const group = await this.getGroupOrThrow(groupId);

    this.assertUserNotInGroup(group, user.id);

    await this.groupUserRepository.save({
      group,
      user,
    });
    return { message: 'User successfully joined group' };
  }

  async removeUserFromGroup(groupId: string, userId: string): Promise<{ message: string }> {
    await this.getGroupOrThrow(groupId);

    const membership = await this.groupUserRepository.findOne({
      where: { group: { id: groupId }, user: { id: userId } },
    });

    if (!membership) {
      throw new NotFoundException(`User with id ${userId} is not a member of this group`);
    }

    await this.groupUserRepository.remove(membership);
    return { message: 'User successfully removed from group' };
  }

  // --------------------------------------------------------------
  //                      Helpers
  // --------------------------------------------------------------
  async getGroupOrThrowAndAssertOwnership(groupId: string, user: UserEntity): Promise<GroupEntity> {
    const group = await this.getGroupOrThrow(groupId);
    this.assertOwnership(group, user);
    return group;
  }

  // --------------------------------------------------------------
  //                      Private methods
  // --------------------------------------------------------------
  async getGroupOrThrow(groupId: string): Promise<GroupEntity> {
    const group = await this.groupRepository.findOne({
      where: { id: groupId },
      relations: ['groupUsers', 'groupUsers.user'],
    });

    if (!group) {
      throw new NotFoundException(`Group with id ${groupId} not found`);
    }

    return group;
  }

  private async assertMembership(groupId: string, userId: string): Promise<void> {
    const membership = await this.groupUserRepository.findOne({
      where: { group: { id: groupId }, user: { id: userId } },
    });

    if (!membership) {
      throw new ForbiddenException('You are not a member of this group');
    }
  }

  private assertUserNotInGroup(group: GroupEntity, userId: string): void {
    //TODO: why groupUser.user.id instead of groupUser.id? Shouldn't we create groupUser with user.id instead of autogenerate id?
    const membership = group.groupUsers.find((groupUser) => groupUser.user.id === userId);

    if (membership) {
      throw new BadRequestException('User is already a member of this group');
    }
  }

  private assertOwnership(group: GroupEntity, user: UserEntity): void {
    if (group.ownerId !== user.id) {
      throw new ForbiddenException('Only the group owner can perform this operation');
    }
  }

  private generateInvitationUrl(groupId: string): string {
    const baseUrl = this.configService.getOrThrow('GROUP_INVITATION_BASE_URL');
    const groupSecret = this.configService.getOrThrow('GROUP_INVITATION_SECRET');
    const groupExpirationTime = this.configService.getOrThrow('GROUP_INVITATION_URL_EXPIRATION');

    const payload: GroupInvitationTokenPayload = {
      groupId,
    };

    const token = this.jwtService.sign(payload, {
      secret: groupSecret,
      expiresIn: groupExpirationTime,
    });

    return `${baseUrl}?token=${token}`;
  }

  private verifyInvitationToken(token: string): GroupInvitationTokenPayload {
    try {
      return this.jwtService.verify(token, {
        secret: this.configService.getOrThrow('GROUP_INVITATION_SECRET'),
      });
    } catch {
      throw new UnauthorizedException('Invalid invitation token');
    }
  }
}
