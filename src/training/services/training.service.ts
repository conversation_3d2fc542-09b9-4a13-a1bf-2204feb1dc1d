import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { TrainingEntity } from '../entities/training.entity';
import { CreateTrainingDto } from '../dto/create-training.dto';
import { UpdateTrainingDto } from '../dto/update-training.dto';
import { UserEntity } from '../../users/entities/user.entity';
import { TrainingStatus } from '../enums/training-status.enum';
import { ExerciseEntity } from '../../exercises/entities/exercise.entity';

@Injectable()
export class TrainingService {
  constructor(
    @InjectRepository(TrainingEntity)
    private trainingRepository: Repository<TrainingEntity>,
    @InjectRepository(ExerciseEntity)
    private exerciseRepository: Repository<ExerciseEntity>,
  ) {}

  /**
   * Filter profanity from text
   * @param text Text to filter
   * @returns Filtered text
   */
  private filterProfanity(text: string): string {
    // Simple profanity filter - in a real app, you'd use a more comprehensive library
    const profanityList = ['brzydkie', 'słowo', 'wulgarny'];
    let filteredText = text;

    profanityList.forEach((word) => {
      const regex = new RegExp(word, 'gi');
      filteredText = filteredText.replace(regex, '&**&');
    });

    return filteredText;
  }

  /**
   * Creates a new training for a user
   * @param createTrainingDto Data for creating the training
   * @param user The user creating the training
   * @returns The created training
   */
  async createTraining(createTrainingDto: CreateTrainingDto, user: UserEntity): Promise<TrainingEntity> {
    try {
      // Filter profanity from name and description
      const filteredName = this.filterProfanity(createTrainingDto.name);
      const filteredDescription = this.filterProfanity(createTrainingDto.description);

      // Verify all exercises exist and belong to the user
      const exercises = await this.exerciseRepository.find({
        where: {
          id: In(createTrainingDto.exercises),
          creatorId: user.id,
        },
      });

      if (exercises.length !== createTrainingDto.exercises.length) {
        throw new BadRequestException('One or more exercises do not exist or do not belong to you');
      }

      const training = this.trainingRepository.create({
        name: filteredName,
        description: filteredDescription,
        tags: createTrainingDto.tags,
        creator: user,
        creatorId: user.id,
        status: TrainingStatus.ACTIVE,
        exercises: exercises,
      });

      return await this.trainingRepository.save(training);
    } catch (error) {
      throw new BadRequestException('Failed to create training: ' + error.message);
    }
  }

  /**
   * Finds all trainings for a user with optional status filtering
   * @param user The user whose trainings to find
   * @param status Optional status filter
   * @returns Array of trainings
   */
  async findAll(user: UserEntity, status?: TrainingStatus): Promise<TrainingEntity[]> {
    try {
      const query = this.trainingRepository
        .createQueryBuilder('training')
        .leftJoinAndSelect('training.exercises', 'exercises')
        .where('training.creatorId = :creatorId', { creatorId: user.id });

      if (status) {
        query.andWhere('training.status = :status', { status });
      }

      return await query.getMany();
    } catch (error) {
      throw new BadRequestException('Failed to retrieve trainings: ' + error.message);
    }
  }

  /**
   * Finds a specific training by ID
   * @param id The training ID
   * @param user The user requesting the training
   * @returns The found training
   */
  async findOne(id: string, user: UserEntity): Promise<TrainingEntity> {
    if (!id) {
      throw new BadRequestException('Training ID is required');
    }

    const training = await this.trainingRepository.findOne({
      where: { id },
      relations: ['exercises'],
    });

    if (!training) {
      throw new NotFoundException(`Training with ID ${id} not found`);
    }

    if (training.creatorId !== user.id) {
      throw new ForbiddenException('You do not have permission to access this training');
    }

    return training;
  }

  /**
   * Updates an existing training
   * @param id The training ID
   * @param updateTrainingDto The update data
   * @param user The user updating the training
   * @returns The updated training
   */
  async update(id: string, updateTrainingDto: UpdateTrainingDto, user: UserEntity): Promise<TrainingEntity> {
    // First verify the training exists and user has permission
    await this.findOne(id, user);

    try {
      // Get the original entity from repository
      const training = await this.trainingRepository.findOne({
        where: { id },
        relations: ['exercises'],
      });

      if (!training) {
        throw new NotFoundException(`Training with ID ${id} not found`);
      }

      // Filter profanity if fields are provided
      if (updateTrainingDto.name) {
        updateTrainingDto.name = this.filterProfanity(updateTrainingDto.name);
      }

      if (updateTrainingDto.description) {
        updateTrainingDto.description = this.filterProfanity(updateTrainingDto.description);
      }

      // Update exercises if provided
      if (updateTrainingDto.exercises) {
        const exercises = await this.exerciseRepository.find({
          where: {
            id: In(updateTrainingDto.exercises),
            creatorId: user.id,
          },
        });

        if (exercises.length !== updateTrainingDto.exercises.length) {
          throw new BadRequestException('One or more exercises do not exist or do not belong to you');
        }

        training.exercises = exercises;
      }

      // Update other fields
      if (updateTrainingDto.name) training.name = updateTrainingDto.name;
      if (updateTrainingDto.description) training.description = updateTrainingDto.description;
      if (updateTrainingDto.tags !== undefined) training.tags = updateTrainingDto.tags;

      // Save the updated training
      return await this.trainingRepository.save(training);
    } catch (error) {
      throw new BadRequestException('Failed to update training: ' + error.message);
    }
  }

  /**
   * Soft deletes a training by setting its status to DELETED
   * @param id The training ID
   * @param user The user deleting the training
   * @returns Object with status
   */
  async remove(id: string, user: UserEntity): Promise<{ status: TrainingStatus }> {
    // First verify the training exists and user has permission
    await this.findOne(id, user);

    try {
      // Set status to deleted instead of actually removing
      await this.trainingRepository.update(id, { status: TrainingStatus.DELETED });

      return {
        status: TrainingStatus.DELETED,
      };
    } catch (error) {
      throw new BadRequestException('Failed to delete training: ' + error.message);
    }
  }
}
