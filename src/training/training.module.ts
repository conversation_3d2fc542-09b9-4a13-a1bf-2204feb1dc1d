import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TrainingController } from './controllers/training.controller';
import { TrainingService } from './services/training.service';
import { TrainingEntity } from './entities/training.entity';
import { AuthModule } from '../auth/auth.module';
import { ExercisesModule } from '../exercises/exercises.module';
import { ExerciseEntity } from '../exercises/entities/exercise.entity';

@Module({
  imports: [TypeOrmModule.forFeature([TrainingEntity, ExerciseEntity]), AuthModule, ExercisesModule],
  controllers: [TrainingController],
  providers: [TrainingService],
  exports: [TrainingService],
})
export class TrainingModule {}
