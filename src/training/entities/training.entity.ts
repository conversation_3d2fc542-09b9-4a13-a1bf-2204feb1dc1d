import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { UserEntity } from '../../users/entities/user.entity';
import { ApiProperty } from '@nestjs/swagger';
import { TrainingStatus } from '../enums/training-status.enum';
import { ExerciseEntity } from '../../exercises/entities/exercise.entity';
import { Exclude } from 'class-transformer';

@Entity({ name: 'trainings' })
export class TrainingEntity {
  @ApiProperty({
    description: 'Unique training identifier',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Training name',
    example: 'Trening na biceps',
  })
  @Column()
  name: string;

  @ApiProperty({
    description: 'Training description',
    example: 'Trening opis super ciało w 2godziny',
  })
  @Column({ type: 'text' })
  description: string;

  @ApiProperty({
    description: 'Training tags',
    example: 'na_cale_cialo|i_na_nogi',
    required: false,
  })
  @Column({ nullable: true })
  tags: string;

  @ApiProperty({
    description: 'Training status',
    enum: TrainingStatus,
    example: TrainingStatus.ACTIVE,
  })
  @Column({
    type: 'enum',
    enum: TrainingStatus,
    default: TrainingStatus.ACTIVE,
  })
  status: TrainingStatus;

  @ApiProperty({
    description: 'Training creation timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'Training last update timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({
    description: 'Creator of the training',
    type: () => UserEntity,
  })
  @ManyToOne(() => UserEntity, (user) => 'trainings', { eager: true })
  @Exclude()
  creator: UserEntity;

  @Column()
  creatorId: string;

  @ApiProperty({
    description: 'Exercises included in the training',
    type: () => [ExerciseEntity],
  })
  @ManyToMany(() => ExerciseEntity)
  @JoinTable({
    name: 'training_exercises',
    joinColumn: {
      name: 'training_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'exercise_id',
      referencedColumnName: 'id',
    },
  })
  exercises: ExerciseEntity[];
}
