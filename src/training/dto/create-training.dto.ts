import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNotEmpty, IsArray } from 'class-validator';

export class CreateTrainingDto {
  @ApiProperty({
    description: 'Training name',
    example: 'Trening na biceps',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Training description',
    example: 'Trening opis super ciało w 2godziny',
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Exercise IDs to include in the training',
    example: ['exercise_id_1', 'exercise_id_2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  exercises: string[];

  @ApiProperty({
    description: 'Training tags (separated by |)',
    example: 'na_cale_cialo|i_na_nogi',
    required: false,
  })
  @IsOptional()
  @IsString()
  tags?: string;
}
