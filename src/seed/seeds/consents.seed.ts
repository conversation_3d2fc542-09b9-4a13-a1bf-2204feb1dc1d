import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { INITIAL_CONSENTS } from '../seed.config';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class ConsentsSeed {
  readonly order = 10;

  constructor(
    private readonly logger: PinoLogger,

    @InjectRepository(ConsentDefinitionEntity)
    private readonly consents: Repository<ConsentDefinitionEntity>,
  ) {
    this.logger.setContext(ConsentsSeed.name);
  }

  async run(): Promise<void> {
    for (const c of INITIAL_CONSENTS) {
      const exists = await this.consents.exist({ where: { code: c.code, version: c.version } });
      if (exists) {
        this.logger.debug(`↔ Consent ${c.code} ${c.version} already exists`);
        continue;
      }
      await this.consents.save(this.consents.create(c));
      this.logger.info(`➕ Created consent ${c.code} ${c.version}`);
    }
  }
}
