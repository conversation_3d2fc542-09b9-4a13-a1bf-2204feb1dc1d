import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { UserEntity } from '@app/users/entities/user.entity';
import { UserConsentEntity } from '@app/consents/entities/user-consent.entity';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { EUserRole } from '@app/constants/models/user-role.enum';

import { USER_SEED } from '../seed.config';

@Injectable()
export class UsersSeed {
  readonly order = 20;

  constructor(
    private readonly logger: PinoLogger,

    @InjectRepository(UserEntity) private readonly users: Repository<UserEntity>,
    @InjectRepository(UserConsentEntity) private readonly userConsents: Repository<UserConsentEntity>,
    @InjectRepository(ConsentDefinitionEntity) private readonly consents: Repository<ConsentDefinitionEntity>,
  ) {
    this.logger.setContext(UsersSeed.name);
  }

  async run(): Promise<void> {
    for (const u of USER_SEED) {
      const user = await this.ensureUser(u.email, u.name, u.password, u.roles ?? [EUserRole.USER]);
      await this.ensureAcceptedAllConsents(user.id);
    }
  }

  private async ensureUser(email: string, name: string, password: string, roles: EUserRole[]): Promise<UserEntity> {
    let user = await this.users.findOne({ where: { email } });

    if (!user) {
      user = this.users.create({ email, name, password, roles });
      await this.users.save(user);
      this.logger.info(`👤 Created user ${email} (${roles.join(', ')})`);
      return user;
    }

    const nextRoles = Array.from(new Set([...(user.roles ?? []), ...roles]));
    if (JSON.stringify(nextRoles) !== JSON.stringify(user.roles ?? [])) {
      user.roles = nextRoles;
      await this.users.save(user);
      this.logger.info(`🔁 Updated roles for ${email}: ${nextRoles.join(', ')}`);
    } else {
      this.logger.debug(`↔ User ${email} already exists`);
    }
    return user;
  }

  private async ensureAcceptedAllConsents(userId: string): Promise<void> {
    const [allConsents, existing] = await Promise.all([
      this.consents.find(),
      this.userConsents.find({ where: { user: { id: userId } } }),
    ]);
    if (!allConsents.length) {
      this.logger.debug('No consents to accept yet');
      return;
    }

    const existingIds = new Set(existing.map((uc) => uc.consentDefinition.id));
    const toAdd = allConsents
      .filter((c) => !existingIds.has(c.id))
      .map((c) => this.userConsents.create({ user: { id: userId } as any, consentDefinition: c }));

    if (toAdd.length) {
      await this.userConsents.save(toAdd);
      this.logger.info(`✅ Added ${toAdd.length} consent(s) to user ${userId}`);
    }
  }
}
