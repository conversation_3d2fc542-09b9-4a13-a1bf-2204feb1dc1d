import { ConsentType } from '@app/constants/models/consent-config';
import { EUserRole } from '@app/constants/models/user-role.enum';

export const INITIAL_CONSENTS = [
  {
    code: ConsentType.POLICY,
    version: 'v1',
    content: 'Akceptuję regulamin serwisu.',
    shortDescription: 'Regulamin',
    isRequired: true,
  },
] as const;

export const USER_SEED: ReadonlyArray<{
  email: string;
  name: string;
  password: string;
  roles?: EUserRole[];
}> = [
  { email: '<EMAIL>', name: 'admin', password: 'Aa123456789!', roles: [EUserRole.SUPER_ADMIN] },
  { email: '<EMAIL>', name: 'user', password: 'zaq1@WSX', roles: [EUserRole.USER] },
  { email: '<EMAIL>', name: 'trainer', password: 'zaq1@WSX', roles: [EUserRole.USER, EUserRole.TRAINER] },
  { email: '<EMAIL>', name: 'diet', password: 'zaq1@WSX', roles: [EUserRole.USER, EUserRole.DIETITIAN] },
  {
    email: '<EMAIL>',
    name: 'all',
    password: 'zaq1@WSX',
    roles: [EUserRole.USER, EUserRole.DIETITIAN, EUserRole.TRAINER],
  },
];
