import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { UserEntity } from '@app/users/entities/user.entity';
import { UserConsentEntity } from '@app/consents/entities/user-consent.entity';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';

import { SeedService } from './seed.service';
import { ConsentsSeed } from './seeds/consents.seed';
import { UsersSeed } from './seeds/users.seed';
import { LoggerModule } from '@app/configs/logger/logger.module';

@Module({
  imports: [LoggerModule, TypeOrmModule.forFeature([UserEntity, UserConsentEntity, ConsentDefinitionEntity])],
  providers: [SeedService, ConsentsSeed, UsersSeed],
  exports: [SeedService],
})
export class SeedModule {}
