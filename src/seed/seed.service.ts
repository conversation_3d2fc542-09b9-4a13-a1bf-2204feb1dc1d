import { Injectable } from '@nestjs/common';
import { ConsentsSeed } from './seeds/consents.seed';
import { UsersSeed } from './seeds/users.seed';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class SeedService {
  private readonly seeds;

  constructor(
    private readonly logger: <PERSON><PERSON>Logger,
    private readonly consentsSeed: ConsentsSeed,
    private readonly usersSeed: UsersSeed,
  ) {
    this.seeds = [consentsSeed, usersSeed];
    this.logger.setContext(SeedService.name);
  }

  async run(): Promise<void> {
    this.logger.warn('Running seeds...');
    const ordered = [...this.seeds].sort((a, b) => a.order - b.order);
    for (const seed of ordered) {
      this.logger.info(`▶ ${seed.constructor.name}`);
      await seed.run();
    }
    this.logger.info('✅ Seeds completed');
  }
}
