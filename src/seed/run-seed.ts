import 'reflect-metadata';
import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { DataSource } from 'typeorm';
import { SeedService } from './seed.service';

const args = new Set(process.argv.slice(2));
const withMigrate = args.has('--migrate');

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule, {
    logger: ['error', 'warn', 'log'],
  });

  try {
    if (withMigrate) {
      const ds = app.get(DataSource);
      await ds.runMigrations();
    }

    const seedService = app.get(SeedService);
    await seedService.run();
    process.exitCode = 0;
  } catch (err) {
    console.error(err);
    process.exitCode = 1;
  } finally {
    await app.close();
  }
}

bootstrap();
