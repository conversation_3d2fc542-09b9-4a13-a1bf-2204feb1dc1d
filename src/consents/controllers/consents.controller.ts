import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { CONSENT_ROUTES } from '@app/constants/routes/consents-routes-names';
import { ConsentsService } from '@app/consents/services/consents.service';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { UserConsentEntity } from '@app/consents/entities/user-consent.entity';
import { CurrentBaseUser } from '@app/tools/decorators/current-user.decorator';
import { UserEntity } from '@app/users/entities/user.entity';
import { JwtAuthGuard } from '@app/tools/guards/jwt-auth.guard';
import { AcceptConsentDto } from '@app/consents/dto/accept-consent.dto';
import { ConsentsSwaggerDocs } from '@app/consents/docs/consents.docs';
import { CreateConsentDto } from '@app/consents/dto/create-consent.dto';

@Controller({ version: '1' })
export class ConsentsController {
  constructor(private readonly consentsService: ConsentsService) {}

  @Post(CONSENT_ROUTES.CREATE_CONSENT)
  @ConsentsSwaggerDocs.createConsentDocs()
  async create(@Body() dto: CreateConsentDto) {
    return this.consentsService.createNewConsent(dto);
  }

  @Get(CONSENT_ROUTES.ALL_LATEST_CONSENTS)
  @ConsentsSwaggerDocs.getAllLatestConsentsDocs()
  async getLatest(): Promise<ConsentDefinitionEntity[]> {
    return this.consentsService.getLatestConsents();
  }

  @Post(CONSENT_ROUTES.ACCEPT_CONSENT)
  @ConsentsSwaggerDocs.acceptConsentDocs()
  async acceptConsents(@Body() data: AcceptConsentDto): Promise<UserConsentEntity[]> {
    return this.consentsService.acceptConsent(data);
  }

  @Get(CONSENT_ROUTES.USER_CONSENTS)
  @ConsentsSwaggerDocs.getUserConsentsDocs()
  @UseGuards(JwtAuthGuard)
  getUserConsents(@CurrentBaseUser() user: UserEntity): Promise<UserConsentEntity[]> {
    return this.consentsService.getUserConsents(user.id);
  }

  @Get(CONSENT_ROUTES.USER_HAS_ACCEPTED_ALL)
  @ConsentsSwaggerDocs.hasAcceptedAllDocs()
  hasAcceptedAll(@Param('id') userId: string): Promise<boolean> {
    return this.consentsService.hasAcceptedAllRequiredConsents(userId);
  }

  @Get(CONSENT_ROUTES.REQUIRED_CONSENTS)
  @ConsentsSwaggerDocs.getRequiredConsentsDocs()
  getRequiredConsents(): Promise<ConsentDefinitionEntity[]> {
    return this.consentsService.getRequiredConsents();
  }
}
