import { Column, <PERSON>tity, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { UserEntity } from '@app/users/entities/user.entity';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';

@Entity({ name: 'user-consents' })
export class UserConsentEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => UserEntity, (user: UserEntity) => user.consents, { onDelete: 'CASCADE' })
  user: UserEntity;

  @ManyToOne(() => ConsentDefinitionEntity, { eager: true })
  consentDefinition: ConsentDefinitionEntity;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  acceptedAt: Date;
}
