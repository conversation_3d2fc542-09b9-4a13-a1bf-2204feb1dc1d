import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';
import { ConsentType } from '@app/constants/models/consent-config';

@Entity({ name: 'consent-definitions' })
@Index(['code', 'version'], { unique: true })
export class ConsentDefinitionEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'enum', enum: ConsentType })
  code: ConsentType;

  @Column()
  version: string;

  @Column()
  content: string;

  @Column({ default: false })
  isRequired: boolean;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;
}
