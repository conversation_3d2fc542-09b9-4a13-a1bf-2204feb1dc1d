import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';
import { CreateGroupRequestDto } from '@app/groups/dto/create-group.dto';
import { addAndAssertUserToGroup, createAndAssertGroup } from '../__helpers/group';
import { randomUUID } from 'crypto';

describe('/GET /v1/group', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should return all groups of the owner', async () => {
    const groupPayload: CreateGroupRequestDto = {
      name: 'Test Group E2E',
      description: 'This is a test group',
      tags: 'e2e|test',
    };
    const groupCount = 5;

    for (let i = 0; i < groupCount; i++) {
      await createAndAssertGroup(app, groupPayload, accessToken);
    }

    const response = await request(app.getHttpServer()).get('/v1/group').set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toBeDefined();
    expect(response.body.length).toBe(groupCount);
    expect(response.body[0].id).toBeDefined();
    expect(response.body[0].name).toBe(groupPayload.name);
    expect(response.body[0].description).toBe(groupPayload.description);
    expect(response.body[0].tags).toBe(groupPayload.tags);
    expect(response.body[0].createdAt).toBeDefined();
    expect(response.body[0].updatedAt).toBeDefined();
    expect(response.body[0].ownerId).toBeDefined();
  });

  it('should return all groups of the member', async () => {
    const groupPayload: CreateGroupRequestDto = {
      name: 'Test Group E2E',
      description: 'This is a test group',
      tags: 'e2e|test',
    };
    const groupCount = 5;
    const groupMember = generateTestUser([consent.id]);
    const groupMemberUser = await registerAndAssertUser(app, groupMember);
    const groupMemberAccessToken = (await loginAndAssertUser(app, groupMember)).accessToken;

    for (let i = 0; i < groupCount; i++) {
      const group = await createAndAssertGroup(app, groupPayload, accessToken);
      await addAndAssertUserToGroup(app, group.id, groupMemberUser.id, accessToken);
    }

    const response = await request(app.getHttpServer())
      .get('/v1/group')
      .set('Authorization', `Bearer ${groupMemberAccessToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toBeDefined();
    expect(response.body.length).toBe(groupCount);
    expect(response.body[0].id).toBeDefined();
    expect(response.body[0].name).toBe(groupPayload.name);
    expect(response.body[0].description).toBe(groupPayload.description);
    expect(response.body[0].tags).toBe(groupPayload.tags);
    expect(response.body[0].createdAt).toBeDefined();
    expect(response.body[0].updatedAt).toBeDefined();
    expect(response.body[0].ownerId).toBeDefined();
    expect(response.body[0].ownerId).not.toBe(groupMemberUser.id);
  });

  it('should return 401 when user is not authenticated', async () => {
    const response = await request(app.getHttpServer()).get('/v1/group');

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });
});

describe('/GET /v1/group/:id', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should return group by id for owner', async () => {
    const groupPayload: CreateGroupRequestDto = {
      name: 'Test Group E2E',
      description: 'This is a test group',
      tags: 'e2e|test',
    };
    const group = await createAndAssertGroup(app, groupPayload, accessToken);

    const response = await request(app.getHttpServer())
      .get(`/v1/group/${group.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toBeDefined();
    expect(response.body.id).toBeDefined();
    expect(response.body.name).toBe(groupPayload.name);
    expect(response.body.description).toBe(groupPayload.description);
    expect(response.body.tags).toBe(groupPayload.tags);
    expect(response.body.createdAt).toBeDefined();
    expect(response.body.updatedAt).toBeDefined();
    expect(response.body.ownerId).toBeDefined();
  });

  it('should return group by id for group member', async () => {
    const groupPayload: CreateGroupRequestDto = {
      name: 'Test Group E2E',
      description: 'This is a test group',
      tags: 'e2e|test',
    };
    const group = await createAndAssertGroup(app, groupPayload, accessToken);

    const groupMember = generateTestUser([consent.id]);
    const groupMemberUser = await registerAndAssertUser(app, groupMember);
    await addAndAssertUserToGroup(app, group.id, groupMemberUser.id, accessToken);

    const response = await request(app.getHttpServer())
      .get(`/v1/group/${group.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toBeDefined();
    expect(response.body.id).toBeDefined();
    expect(response.body.name).toBe(groupPayload.name);
    expect(response.body.description).toBe(groupPayload.description);
    expect(response.body.tags).toBe(groupPayload.tags);
    expect(response.body.createdAt).toBeDefined();
    expect(response.body.updatedAt).toBeDefined();
    expect(response.body.ownerId).toBeDefined();
  });

  it('should return 401 when user is not authenticated', async () => {
    const groupPayload: CreateGroupRequestDto = {
      name: 'Test Group E2E',
      description: 'This is a test group',
      tags: 'e2e|test',
    };
    const group = await createAndAssertGroup(app, groupPayload, accessToken);
    const response = await request(app.getHttpServer()).get(`/v1/group/${group.id}`);

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });

  it('should return 404 when group id is invalid or not found', async () => {
    const id = randomUUID();
    const response = await request(app.getHttpServer())
      .get(`/v1/group/${id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(404);
    expect(response.body.message).toBe(`Group with id ${id} not found`);
  });

  it('should return 403 when user is not a member of the group', async () => {
    const groupPayload: CreateGroupRequestDto = {
      name: 'Test Group E2E',
      description: 'This is a test group',
      tags: 'e2e|test',
    };
    const group = await createAndAssertGroup(app, groupPayload, accessToken);

    const groupMember = generateTestUser([consent.id]);
    const groupMemberUser = await registerAndAssertUser(app, groupMember);
    await addAndAssertUserToGroup(app, group.id, groupMemberUser.id, accessToken);

    const notGroupMember = generateTestUser([consent.id]);
    await registerAndAssertUser(app, notGroupMember);
    const notGroupMemberAccessToken = (await loginAndAssertUser(app, notGroupMember)).accessToken;

    const response = await request(app.getHttpServer())
      .get(`/v1/group/${group.id}`)
      .set('Authorization', `Bearer ${notGroupMemberAccessToken}`);

    expect(response.status).toBe(403);
    expect(response.body.message).toBe('You are not a member of this group');
  });
});
